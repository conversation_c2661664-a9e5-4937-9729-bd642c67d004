"""
KPI Caching System
Provides intelligent caching for KPI calculations with role-based cache keys
and automatic invalidation when underlying data changes.
"""

from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from typing import Optional, List, Dict, Any
import hashlib
import json
import logging

logger = logging.getLogger(__name__)


class KPICacheManager:
    """
    Advanced cache management for KPI system with role-based caching
    and intelligent invalidation strategies.
    """
    
    CACHE_PREFIX = 'kpi'
    DEFAULT_TIMEOUT = 300  # 5 minutes
    LONG_TIMEOUT = 1800    # 30 minutes
    SHORT_TIMEOUT = 60     # 1 minute
    
    # Cache timeouts by KPI type
    TIMEOUT_BY_TYPE = {
        'FINANCIAL': LONG_TIMEOUT,      # Financial KPIs change less frequently
        'OPERATIONAL': DEFAULT_TIMEOUT,  # Standard timeout
        'CUSTOMER': DEFAULT_TIMEOUT,
        'EMPLOYEE': SHORT_TIMEOUT,      # Employee KPIs change more frequently
        'ASSET': LONG_TIMEOUT,
        'CUSTOM': DEFAULT_TIMEOUT,
    }
    
    @classmethod
    def get_cache_key(cls, kpi_id: str, period_start, period_end, 
                     user_role: str = None, department_id: str = None) -> str:
        """
        Generate cache key for KPI values with role and department context
        """
        key_components = [
            str(kpi_id),
            period_start.isoformat() if period_start else 'none',
            period_end.isoformat() if period_end else 'none',
            user_role or 'all',
            department_id or 'all'
        ]
        key_data = ':'.join(key_components)
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"{cls.CACHE_PREFIX}:value:{key_hash}"
    
    @classmethod
    def get_dashboard_cache_key(cls, user_role: str, department_id: str = None) -> str:
        """Generate cache key for dashboard data"""
        key_data = f"dashboard:{user_role}:{department_id or 'all'}"
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"{cls.CACHE_PREFIX}:dashboard:{key_hash}"
    
    @classmethod
    def cache_kpi_value(cls, kpi_id: str, value: Any, period_start, period_end,
                       user_role: str = None, department_id: str = None,
                       kpi_type: str = 'OPERATIONAL') -> None:
        """
        Cache KPI value with appropriate timeout based on KPI type
        """
        cache_key = cls.get_cache_key(kpi_id, period_start, period_end, user_role, department_id)
        timeout = cls.TIMEOUT_BY_TYPE.get(kpi_type, cls.DEFAULT_TIMEOUT)
        
        cache_data = {
            'value': str(value) if value is not None else None,
            'cached_at': timezone.now().isoformat(),
            'kpi_id': kpi_id,
            'user_role': user_role,
            'department_id': department_id
        }
        
        cache.set(cache_key, cache_data, timeout)
        logger.debug(f"Cached KPI {kpi_id} value: {value} for {timeout}s")
    
    @classmethod
    def get_cached_kpi_value(cls, kpi_id: str, period_start, period_end,
                            user_role: str = None, department_id: str = None) -> Optional[Any]:
        """
        Get cached KPI value
        """
        cache_key = cls.get_cache_key(kpi_id, period_start, period_end, user_role, department_id)
        cached_data = cache.get(cache_key)
        
        if cached_data:
            logger.debug(f"Cache hit for KPI {kpi_id}")
            return cached_data.get('value')
        
        logger.debug(f"Cache miss for KPI {kpi_id}")
        return None
    
    @classmethod
    def cache_dashboard_data(cls, dashboard_data: Dict, user_role: str, 
                           department_id: str = None) -> None:
        """
        Cache dashboard data for specific role and department
        """
        cache_key = cls.get_dashboard_cache_key(user_role, department_id)
        
        cache_data = {
            'data': dashboard_data,
            'cached_at': timezone.now().isoformat(),
            'user_role': user_role,
            'department_id': department_id
        }
        
        cache.set(cache_key, cache_data, cls.DEFAULT_TIMEOUT)
        logger.debug(f"Cached dashboard data for role {user_role}")
    
    @classmethod
    def get_cached_dashboard_data(cls, user_role: str, department_id: str = None) -> Optional[Dict]:
        """
        Get cached dashboard data
        """
        cache_key = cls.get_dashboard_cache_key(user_role, department_id)
        cached_data = cache.get(cache_key)
        
        if cached_data:
            logger.debug(f"Dashboard cache hit for role {user_role}")
            return cached_data.get('data')
        
        logger.debug(f"Dashboard cache miss for role {user_role}")
        return None
    
    @classmethod
    def invalidate_kpi_cache(cls, kpi_id: str) -> None:
        """
        Invalidate all cached values for a specific KPI
        """
        # Since we use hashed keys, we need to use a pattern-based approach
        # This is a simplified version - in production, consider using Redis with pattern matching
        cache_pattern = f"{cls.CACHE_PREFIX}:value:*"
        
        # For now, we'll clear the entire KPI cache
        # In production, implement more granular invalidation
        cls.clear_all_kpi_cache()
        logger.info(f"Invalidated cache for KPI {kpi_id}")
    
    @classmethod
    def invalidate_dashboard_cache(cls, user_role: str = None) -> None:
        """
        Invalidate dashboard cache for specific role or all roles
        """
        if user_role:
            cache_key = cls.get_dashboard_cache_key(user_role)
            cache.delete(cache_key)
            logger.info(f"Invalidated dashboard cache for role {user_role}")
        else:
            # Clear all dashboard caches
            cls.clear_dashboard_cache()
            logger.info("Invalidated all dashboard caches")
    
    @classmethod
    def clear_all_kpi_cache(cls) -> None:
        """
        Clear all KPI-related cache entries
        """
        # This is a simplified implementation
        # In production with Redis, use SCAN with pattern matching
        try:
            cache.clear()
            logger.info("Cleared all KPI cache entries")
        except Exception as e:
            logger.error(f"Error clearing KPI cache: {e}")
    
    @classmethod
    def clear_dashboard_cache(cls) -> None:
        """
        Clear all dashboard cache entries
        """
        try:
            # In production, implement pattern-based deletion
            cache.clear()
            logger.info("Cleared all dashboard cache entries")
        except Exception as e:
            logger.error(f"Error clearing dashboard cache: {e}")
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """
        Get cache statistics for monitoring
        """
        # This would need to be implemented based on your cache backend
        # For Redis, you could use INFO command
        return {
            'cache_backend': str(cache.__class__),
            'timestamp': timezone.now().isoformat(),
            'status': 'active'
        }


class KPIBatchProcessor:
    """
    Batch processor for calculating multiple KPIs efficiently
    """
    
    def __init__(self, cache_manager: KPICacheManager = None):
        self.cache_manager = cache_manager or KPICacheManager()
        self.batch_size = 50
    
    def calculate_kpis_batch(self, kpi_ids: List[str], period_start, period_end,
                           user_role: str = None, department_id: str = None) -> Dict[str, Any]:
        """
        Calculate multiple KPIs in batch for better performance
        """
        results = {}
        cached_results = {}
        uncached_kpi_ids = []
        
        # Check cache first
        for kpi_id in kpi_ids:
            cached_value = self.cache_manager.get_cached_kpi_value(
                kpi_id, period_start, period_end, user_role, department_id
            )
            if cached_value is not None:
                cached_results[kpi_id] = cached_value
            else:
                uncached_kpi_ids.append(kpi_id)
        
        # Calculate uncached KPIs
        if uncached_kpi_ids:
            from .models import KPI
            # Enhanced KPI engine removed - using current values

            kpis = KPI.objects.filter(
                id__in=uncached_kpi_ids,
                status='ACTIVE'
            ).select_related('category')

            for kpi in kpis:
                try:
                    # Use current value from KPI model
                    value = kpi.current_value
                    if value is not None:
                        results[str(kpi.id)] = value
                        # Cache the result
                        self.cache_manager.cache_kpi_value(
                            str(kpi.id), value, period_start, period_end,
                            user_role, department_id, kpi.category.name if kpi.category else 'OPERATIONAL'
                        )
                except Exception as e:
                    logger.error(f"Error getting KPI {kpi.id} value: {e}")
                    results[str(kpi.id)] = None
        
        # Combine cached and calculated results
        results.update(cached_results)
        
        logger.info(f"Batch processed {len(kpi_ids)} KPIs: {len(cached_results)} from cache, {len(results) - len(cached_results)} calculated")
        
        return results
