/**
 * Enhanced Navigation Configuration with Role-Based KPI Sections
 * Implements enterprise-grade role-based navigation with hierarchical access patterns
 * 
 * Features:
 * - Role-specific KPI dashboard sections
 * - Hierarchical data access navigation
 * - Manager-subordinate navigation patterns
 * - Department and project scoped navigation
 * - Real-time KPI status indicators
 */

import {
  Home,
  BarChart3,
  TrendingUp,
  Users,
  Building,
  Briefcase,
  DollarSign,
  Target,
  Activity,
  PieChart,
  Calendar,
  FileText,
  Settings,
  Shield,
  Monitor,
  GitBranch,
  UserCheck,
  Heart,
  Receipt,
  CheckSquare,
  User,
  Megaphone,
  Clock,
  Award,
  BookOpen
} from 'lucide-react'

export interface EnhancedNavigationItem {
  name: string
  nameAr?: string
  icon: any
  href?: string
  id?: string
  children?: EnhancedNavigationItem[]
  badge?: {
    count?: number
    color?: 'red' | 'yellow' | 'green' | 'blue'
    text?: string
  }
  permissions?: string[]
  kpiCategory?: string
  realTimeUpdates?: boolean
}

export interface EnhancedNavigationTranslations {
  [key: string]: string
}

/**
 * Enhanced role-based navigation configuration with KPI sections
 */
export const createEnhancedNavigationConfig = (t: EnhancedNavigationTranslations) => ({
  super_admin: [
    // Main Dashboard
    { name: t.dashboard, icon: Home, href: '/superadmin/dashboard' },

    // Core System Management (EXISTING ROUTES ONLY)
    {
      name: 'System Management',
      nameAr: 'إدارة النظام',
      icon: Settings,
      id: 'system-management',
      children: [
        { name: 'User Management', nameAr: 'إدارة المستخدمين', icon: Users, href: '/superadmin/users' },
        { name: 'Organizations', nameAr: 'المنظمات', icon: Building, href: '/superadmin/organizations' },
        { name: 'Reports', nameAr: 'التقارير', icon: FileText, href: '/superadmin/reports' },
        { name: 'Settings', nameAr: 'الإعدادات', icon: Settings, href: '/superadmin/settings' }
      ]
    },

    // KPI Analytics - Simplified (use tabs within dashboard)
    {
      name: 'KPI Analytics',
      nameAr: 'تحليلات مؤشرات الأداء',
      icon: BarChart3,
      href: '/superadmin/kpi/executive',
      realTimeUpdates: true
    },
    {
      name: 'HR Analytics',
      nameAr: 'تحليلات الموارد البشرية',
      icon: Users,
      href: '/superadmin/kpi/hr',
      realTimeUpdates: true
    },
    {
      name: 'Financial Analytics',
      nameAr: 'التحليلات المالية',
      icon: DollarSign,
      href: '/superadmin/kpi/financial',
      realTimeUpdates: true
    },
    {
      name: 'Department & Project Analytics',
      nameAr: 'تحليلات الأقسام والمشاريع',
      icon: Building,
      id: 'dept-project-analytics',
      kpiCategory: 'department',
      realTimeUpdates: true,
      children: [
        {
          name: 'Department Overview',
          nameAr: 'نظرة عامة على الأقسام',
          icon: Building,
          href: '/superadmin/kpi/department',
          realTimeUpdates: true,
          badge: { color: 'blue' as const, text: 'Dept' }
        },
        {
          name: 'Project Overview',
          nameAr: 'نظرة عامة على المشاريع',
          icon: Briefcase,
          href: '/superadmin/kpi/project',
          realTimeUpdates: true,
          badge: { color: 'yellow' as const, text: 'Projects' }
        }
      ]
    },

    // HR Management (EXISTING ROUTES)
    {
      name: 'HR Management',
      nameAr: 'إدارة الموارد البشرية',
      icon: Users,
      id: 'hr-management',
      children: [
        { name: 'Leave Management', nameAr: 'إدارة الإجازات', icon: Calendar, href: '/superadmin/hr/leave' },
        { name: 'Attendance', nameAr: 'الحضور', icon: Clock, href: '/superadmin/hr/attendance' },
        { name: 'Performance', nameAr: 'الأداء', icon: TrendingUp, href: '/superadmin/hr/performance' },
        { name: 'Payroll', nameAr: 'كشوف المرتبات', icon: DollarSign, href: '/superadmin/hr/payroll' }
      ]
    },

    // Sales Management (EXISTING ROUTES)
    {
      name: 'Sales Management',
      nameAr: 'إدارة المبيعات',
      icon: DollarSign,
      id: 'sales-management',
      children: [
        { name: 'Sales Orders', nameAr: 'طلبات المبيعات', icon: FileText, href: '/superadmin/sales/orders' },
        { name: 'Quotations', nameAr: 'عروض الأسعار', icon: FileText, href: '/superadmin/sales/quotations' },
        { name: 'Sales Pipeline', nameAr: 'خط أنابيب المبيعات', icon: TrendingUp, href: '/superadmin/sales/pipeline' }
      ]
    },

    // Legacy Analytics (EXISTING ROUTES)
    {
      name: 'Legacy Analytics',
      nameAr: 'التحليلات القديمة',
      icon: BarChart3,
      id: 'legacy-analytics',
      children: [
        { name: 'Business Intelligence', nameAr: 'ذكاء الأعمال', icon: BarChart3, href: '/admin/business-intelligence' },
        { name: 'Advanced Dashboard', nameAr: 'لوحة التحكم المتقدمة', icon: Monitor, href: '/admin/advanced-dashboard' },
        { name: 'KPI Dashboard', nameAr: 'لوحة مؤشرات الأداء', icon: Target, href: '/admin/kpi/dashboard' },
        { name: 'KPI Management', nameAr: 'إدارة مؤشرات الأداء', icon: Settings, href: '/admin/kpi/management' }
      ]
    },

    // System Administration (EXISTING ROUTES)
    {
      name: 'System Administration',
      nameAr: 'إدارة النظام',
      icon: Settings,
      id: 'system-admin',
      children: [
        { name: 'System Settings', nameAr: 'إعدادات النظام', icon: Settings, href: '/admin/system' },
        { name: 'Security Center', nameAr: 'مركز الأمان', icon: Shield, href: '/admin/security' },
        { name: 'AI Management', nameAr: 'إدارة الذكاء الاصطناعي', icon: Activity, href: '/admin/ai' },
        { name: 'Advanced Analytics', nameAr: 'التحليلات المتقدمة', icon: BarChart3, href: '/admin/analytics' },
        { name: 'Compliance Center', nameAr: 'مركز الامتثال', icon: Shield, href: '/admin/compliance' }
      ]
    }
  ],

  admin: [
    { name: t.dashboard, icon: Home, href: '/admin/dashboard' },
    {
      name: 'Administrative Analytics',
      nameAr: 'التحليلات الإدارية',
      icon: BarChart3,
      id: 'admin-analytics',
      kpiCategory: 'administrative',
      realTimeUpdates: true,
      children: [
        { 
          name: 'Operational Dashboard', 
          nameAr: 'لوحة العمليات',
          icon: Activity, 
          href: '/admin/kpi/operational',
          realTimeUpdates: true
        },
        { 
          name: 'Department Performance', 
          nameAr: 'أداء الأقسام',
          icon: Building, 
          href: '/admin/kpi/departments',
          realTimeUpdates: true
        },
        { 
          name: 'Resource Utilization', 
          nameAr: 'استخدام الموارد',
          icon: PieChart, 
          href: '/admin/kpi/resources',
          realTimeUpdates: true
        }
      ]
    },
    { name: t.users, icon: Users, href: '/admin/users' },
    { name: t.departments, icon: Building, href: '/admin/departments' },
    { name: t.reports, icon: FileText, href: '/admin/reports' },
    { name: t.settings, icon: Settings, href: '/admin/settings' }
  ],

  hr_manager: [
    { name: t.dashboard, icon: Home, href: '/hr/dashboard' },
    { name: t.employees, icon: Users, href: '/hr/employees' },
    { name: t.attendance, icon: Calendar, href: '/hr/attendance' },
    { name: t.leaveManagement, icon: FileText, href: '/hr/leave' },
    { name: t.performance, icon: TrendingUp, href: '/hr/performance' },
    { name: t.payroll, icon: DollarSign, href: '/hr/payroll' }
  ],

  finance_manager: [
    { name: t.dashboard, icon: Home, href: '/finance/dashboard' },
    {
      name: 'Financial Analytics',
      nameAr: 'التحليلات المالية',
      icon: DollarSign,
      id: 'financial-analytics',
      kpiCategory: 'financial',
      realTimeUpdates: true,
      children: [
        { 
          name: 'Revenue Dashboard', 
          nameAr: 'لوحة الإيرادات',
          icon: TrendingUp, 
          href: '/finance/kpi/revenue',
          realTimeUpdates: true,
          badge: { color: 'green' as const, text: 'Live' }
        },
        { 
          name: 'Profitability Metrics', 
          nameAr: 'مقاييس الربحية',
          icon: PieChart, 
          href: '/finance/kpi/profitability',
          realTimeUpdates: true
        },
        { 
          name: 'Cash Flow Analytics', 
          nameAr: 'تحليلات التدفق النقدي',
          icon: Activity, 
          href: '/finance/kpi/cashflow',
          realTimeUpdates: true
        },
        { 
          name: 'Budget Performance', 
          nameAr: 'أداء الميزانية',
          icon: Target, 
          href: '/finance/kpi/budget',
          realTimeUpdates: true
        },
        { 
          name: 'Cost Analysis', 
          nameAr: 'تحليل التكاليف',
          icon: Receipt, 
          href: '/finance/kpi/costs',
          realTimeUpdates: true
        }
      ]
    },
    { name: t.budgets, icon: PieChart, href: '/finance/budgets' },
    { name: t.reports, icon: FileText, href: '/finance/reports' },
    { name: t.expenses, icon: Receipt, href: '/finance/expenses' }
  ],

  department_manager: [
    { name: t.dashboard, icon: Home, href: '/department/dashboard' },
    {
      name: 'Department Analytics',
      nameAr: 'تحليلات القسم',
      icon: Building,
      id: 'department-analytics',
      kpiCategory: 'department',
      realTimeUpdates: true,
      children: [
        { 
          name: 'Team Performance', 
          nameAr: 'أداء الفريق',
          icon: Users, 
          href: '/department/kpi/team-performance',
          realTimeUpdates: true,
          badge: { color: 'blue' as const, text: 'Team' }
        },
        { 
          name: 'Productivity Metrics', 
          nameAr: 'مقاييس الإنتاجية',
          icon: TrendingUp, 
          href: '/department/kpi/productivity',
          realTimeUpdates: true
        },
        { 
          name: 'Resource Utilization', 
          nameAr: 'استخدام الموارد',
          icon: PieChart, 
          href: '/department/kpi/resources',
          realTimeUpdates: true
        },
        { 
          name: 'Department Goals', 
          nameAr: 'أهداف القسم',
          icon: Target, 
          href: '/department/kpi/goals',
          realTimeUpdates: true
        }
      ]
    },
    { name: t.team, icon: Users, href: '/department/team' },
    { name: t.projects, icon: Briefcase, href: '/department/projects' },
    { name: t.tasks, icon: CheckSquare, href: '/department/tasks' }
  ],

  project_manager: [
    { name: t.dashboard, icon: Home, href: '/projects/dashboard' },
    {
      name: 'Project Analytics',
      nameAr: 'تحليلات المشاريع',
      icon: Briefcase,
      id: 'project-analytics',
      kpiCategory: 'project',
      realTimeUpdates: true,
      children: [
        { 
          name: 'Project Performance', 
          nameAr: 'أداء المشاريع',
          icon: TrendingUp, 
          href: '/projects/kpi/performance',
          realTimeUpdates: true,
          badge: { color: 'blue' as const, text: 'Live' }
        },
        { 
          name: 'Timeline Tracking', 
          nameAr: 'تتبع الجدول الزمني',
          icon: Clock, 
          href: '/projects/kpi/timeline',
          realTimeUpdates: true
        },
        { 
          name: 'Resource Allocation', 
          nameAr: 'تخصيص الموارد',
          icon: PieChart, 
          href: '/projects/kpi/resources',
          realTimeUpdates: true
        },
        { 
          name: 'Quality Metrics', 
          nameAr: 'مقاييس الجودة',
          icon: Award, 
          href: '/projects/kpi/quality',
          realTimeUpdates: true
        }
      ]
    },
    { name: t.projects, icon: Briefcase, href: '/projects/list' },
    { name: t.tasks, icon: CheckSquare, href: '/projects/tasks' },
    { name: t.team, icon: Users, href: '/projects/team' }
  ],

  employee: [
    { name: t.dashboard, icon: Home, href: '/employee/dashboard' },
    {
      name: 'Personal Analytics',
      nameAr: 'التحليلات الشخصية',
      icon: User,
      id: 'personal-analytics',
      kpiCategory: 'personal',
      realTimeUpdates: true,
      children: [
        { 
          name: 'My Performance', 
          nameAr: 'أدائي',
          icon: TrendingUp, 
          href: '/employee/kpi/performance',
          realTimeUpdates: true,
          badge: { color: 'green' as const, text: 'Personal' }
        },
        { 
          name: 'Goal Progress', 
          nameAr: 'تقدم الأهداف',
          icon: Target, 
          href: '/employee/kpi/goals',
          realTimeUpdates: true
        },
        { 
          name: 'Productivity Score', 
          nameAr: 'نقاط الإنتاجية',
          icon: Activity, 
          href: '/employee/kpi/productivity',
          realTimeUpdates: true
        }
      ]
    },
    { name: t.myProfile, icon: User, href: '/employee/profile' },
    { name: t.myTasks, icon: CheckSquare, href: '/employee/tasks' },
    { name: t.mySchedule, icon: Calendar, href: '/employee/schedule' },
    {
      name: t.companyInfo,
      icon: Building,
      id: 'company',
      children: [
        { name: t.announcements, icon: Megaphone, href: '/employee/communication/announcements' },
        { name: t.documents, icon: FileText, href: '/employee/communication/documents' }
      ]
    }
  ],

  intern: [
    { name: t.dashboard, icon: Home, href: '/employee/dashboard' },
    {
      name: 'Learning Analytics',
      nameAr: 'تحليلات التعلم',
      icon: BookOpen,
      id: 'learning-analytics',
      kpiCategory: 'learning',
      realTimeUpdates: true,
      children: [
        { 
          name: 'Learning Progress', 
          nameAr: 'تقدم التعلم',
          icon: TrendingUp, 
          href: '/employee/kpi/learning',
          realTimeUpdates: true,
          badge: { color: 'blue' as const, text: 'Learning' }
        },
        { 
          name: 'Skill Development', 
          nameAr: 'تطوير المهارات',
          icon: Award, 
          href: '/employee/kpi/skills',
          realTimeUpdates: true
        },
        { 
          name: 'Task Completion', 
          nameAr: 'إنجاز المهام',
          icon: CheckSquare, 
          href: '/employee/kpi/tasks',
          realTimeUpdates: true
        }
      ]
    },
    { name: t.myProfile, icon: User, href: '/employee/profile' },
    { name: t.myTasks, icon: CheckSquare, href: '/employee/tasks' },
    { name: t.mySchedule, icon: Calendar, href: '/employee/schedule' },
    {
      name: t.companyInfo,
      icon: Building,
      id: 'company',
      children: [
        { name: t.announcements, icon: Megaphone, href: '/employee/communication/announcements' },
        { name: t.documents, icon: FileText, href: '/employee/communication/documents' }
      ]
    }
  ]
})

/**
 * Get enhanced navigation for a specific role with KPI sections
 */
export const getEnhancedNavigationForRole = (
  userRole: string, 
  t: EnhancedNavigationTranslations
): EnhancedNavigationItem[] => {
  const config = createEnhancedNavigationConfig(t)

  if (config[userRole]) {
    return config[userRole]
  }

  // Log warning for unknown roles in development
  if (process.env.NODE_ENV === 'development') {
    console.warn(`Enhanced Navigation: Unknown user role '${userRole}', falling back to employee navigation`)
  }

  // Fallback to employee navigation
  return config.employee || []
}

/**
 * Get KPI-specific navigation items for a role
 */
export const getKPINavigationForRole = (userRole: string, t: EnhancedNavigationTranslations) => {
  const navigation = getEnhancedNavigationForRole(userRole, t)
  return navigation.filter(item => item.kpiCategory || item.id?.includes('analytics'))
}

/**
 * Check if a navigation item has real-time updates
 */
export const hasRealTimeUpdates = (item: EnhancedNavigationItem): boolean => {
  return item.realTimeUpdates || false
}
