/**
 * Unified KPI Dashboard Page
 * Single component that handles all KPI dashboard types through URL parameters
 * Consolidates: BusinessIntelligence, ExecutiveKPIDashboard, and KPIDashboard
 */

import React, { useMemo } from 'react'
import { useSearchParams, useLocation } from 'react-router-dom'
import HierarchicalKPIDashboard from '../../components/kpi/HierarchicalKPIDashboard'

interface UnifiedKPIDashboardProps {
  language?: 'ar' | 'en'
  defaultType?: 'executive' | 'business-intelligence' | 'general' | 'hr' | 'financial'
}

const UnifiedKPIDashboard: React.FC<UnifiedKPIDashboardProps> = ({ 
  language = 'en',
  defaultType = 'general'
}) => {
  const [searchParams] = useSearchParams()
  const location = useLocation()
  
  // Get dashboard type from URL parameters or default
  const dashboardType = searchParams.get('type') || defaultType
  
  // Configuration for different dashboard types
  const dashboardConfig = useMemo(() => {
    switch (dashboardType) {
      case 'executive':
        return {
          component: 'hierarchical',
          dashboardType: 'executive' as const,
          title: language === 'ar' ? 'لوحة مؤشرات الأداء التنفيذية' : 'Executive KPI Dashboard',
          description: language === 'ar' 
            ? 'مؤشرات الأداء الرئيسية على مستوى المؤسسة للقيادة التنفيذية'
            : 'Enterprise-level KPIs for executive leadership',
          background: 'from-gray-900 via-blue-900 to-purple-900',
          kpiFilters: {
            categories: ['Executive', 'Strategic', 'Organizational'],
            kpiType: 'executive'
          }
        }
        
      case 'business-intelligence':
        return {
          component: 'hierarchical',
          dashboardType: 'executive' as const,
          title: language === 'ar' ? 'ذكاء الأعمال' : 'Business Intelligence',
          description: language === 'ar'
            ? 'تحليلات شاملة وتقارير ذكية لاتخاذ القرارات الاستراتيجية'
            : 'Comprehensive analytics and intelligent reports for strategic decision making',
          background: 'from-gray-900 via-indigo-900 to-purple-900',
          kpiFilters: {
            categories: ['Business Intelligence', 'Analytics', 'Strategic', 'Executive'],
            kpiType: 'business-intelligence'
          }
        }
        
      case 'hr':
        return {
          component: 'hierarchical',
          dashboardType: 'hr' as const,
          title: language === 'ar' ? 'تحليلات الموارد البشرية' : 'HR Analytics Dashboard',
          description: language === 'ar'
            ? 'مؤشرات الأداء الخاصة بإدارة الموارد البشرية والموظفين'
            : 'Human resources and employee performance indicators',
          background: 'from-gray-900 via-green-900 to-teal-900',
          kpiFilters: {
            categories: ['Human Resources', 'HR', 'Employee', 'Retention', 'Satisfaction'],
            kpiType: 'hr'
          }
        }
        
      case 'financial':
        return {
          component: 'hierarchical',
          dashboardType: 'financial' as const,
          title: language === 'ar' ? 'التحليلات المالية' : 'Financial Analytics Dashboard',
          description: language === 'ar'
            ? 'مؤشرات الأداء المالي والميزانية والربحية'
            : 'Financial performance, budget, and profitability indicators',
          background: 'from-gray-900 via-yellow-900 to-orange-900',
          kpiFilters: {
            categories: ['Financial', 'Revenue', 'Budget', 'Profitability'],
            kpiType: 'financial'
          }
        }
        
      default: // 'general'
        return {
          component: 'hierarchical',
          dashboardType: 'executive' as const,
          title: language === 'ar' ? 'لوحة مؤشرات الأداء' : 'KPI Dashboard',
          description: language === 'ar'
            ? 'مؤشرات الأداء الرئيسية العامة للمؤسسة'
            : 'General organizational key performance indicators',
          background: 'from-gray-900 via-slate-900 to-zinc-900',
          kpiFilters: {
            categories: ['General', 'Organizational', 'Performance'],
            kpiType: 'general'
          }
        }
    }
  }, [dashboardType, language])

  // Render Hierarchical KPI Dashboard for all dashboard types
  return (
    <div className={`min-h-screen bg-gradient-to-br ${dashboardConfig.background} p-6`}>
      {/* Dashboard Header */}
      <div className="mb-6">
        <div className="mb-4 p-4 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
          <h1 className="text-3xl font-bold text-white mb-2">
            {dashboardConfig.title}
          </h1>
          <p className="text-white/80 text-sm mb-2">
            {dashboardConfig.description}
          </p>
          <div className="flex items-center gap-4 text-xs text-white/60">
            <span>📊 Type: {dashboardType}</span>
            <span>🔗 Route: {location.pathname}</span>
            <span>🌐 Language: {language}</span>
          </div>
        </div>
      </div>

      {/* KPI Dashboard */}
      <HierarchicalKPIDashboard
        dashboardType={dashboardConfig.dashboardType}
        className="max-w-7xl mx-auto"
        kpiFilters={dashboardConfig.kpiFilters}
      />
    </div>
  )
}

export default UnifiedKPIDashboard
