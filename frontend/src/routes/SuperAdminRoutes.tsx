import { Routes, Route, Navigate } from 'react-router-dom'
import RoleBasedRoute from '../components/RoleBasedRoute'
import { LazyErrorBoundary } from '../utils/lazyLoad'

// Import lazy-loaded components
import {
  Unauthorized,
  SuperAdminDashboard,
  Employees,
  Departments,
  Reports,
  Settings,
  LeaveManagement,
  Attendance,
  Performance,
  Payroll,
  Projects,
  Tasks,
  ProjectReports,
  FinanceBudgets,
  FinanceReports,
  Assets,
  Suppliers,
  PurchaseOrders,
  Messages,
  Announcements,
  Documents,
  Meetings,
  PersonalProfile,
  PersonalMessages,
  PersonalCalendar,
  AnalyticsComponent,
  EmployeeProfile,
  EmployeeLeave,
  EmployeeTasks,
  Inventory,
  UserManagement,
  SalesOrders,
  Quotations,
  SalesPipeline,
  Calendar,
  BusinessIntelligence,
  VendorManagement,
  Customers,
  Products,
  WorkflowAutomation,
  ReportGenerator,
  AdvancedDashboard,
  KPIDashboard,
  KPIManagement,
  UnifiedKPIDashboard,
  SystemAdministration,
  SecurityCenter,
  AIManagement,
  AdvancedAnalytics,
  ComplianceCenter,
  SuperAdminSystemSettings,
  SuperAdminSecurityCenter,
  // KPI Analytics Components
  ExecutiveKPIDashboard,
  HRAnalyticsDashboard,
  FinancialAnalyticsDashboard,
  DepartmentAnalyticsDashboard,
  ProjectAnalyticsDashboard
} from './lazyRoutes'

// Enterprise Components
import { MLDashboard } from '../components/enterprise'

interface SuperAdminRoutesProps {
  language: 'ar' | 'en'
}

export default function SuperAdminRoutes({ language }: SuperAdminRoutesProps) {
  return (
    <LazyErrorBoundary>
      <Routes>
      {/* Dashboard - Root redirects to /superadmin/dashboard */}
      <Route path="/" element={<Navigate to="/superadmin/dashboard" replace />} />
      <Route path="/superadmin/dashboard" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <SuperAdminDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* System Management */}
      <Route path="/superadmin/employees" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <Employees language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/users" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <UserManagement language={language} />
        </RoleBasedRoute>
      } />
      {/* ROUTE FIX: Add singular /superadmin/user route for compatibility */}
      <Route path="/superadmin/user" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <UserManagement language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/organizations" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <Departments language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/reports" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <Reports language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/settings" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <Settings language={language} />
        </RoleBasedRoute>
      } />

      {/* HR Management - FIXED: Added main HR dashboard route */}
      <Route path="/superadmin/hr" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <HRAnalyticsDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/hr/dashboard" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <HRAnalyticsDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/hr/leave" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <LeaveManagement language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/hr/attendance" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <Attendance language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/hr/performance" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <Performance language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/hr/payroll" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <Payroll language={language} />
        </RoleBasedRoute>
      } />

      {/* Finance Management - FIXED: Added main Finance dashboard route */}
      <Route path="/superadmin/finance" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <FinancialAnalyticsDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/finance/dashboard" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <FinancialAnalyticsDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/finance/budgets" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <FinanceBudgets language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/finance/reports" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <FinanceReports language={language} />
        </RoleBasedRoute>
      } />

      {/* Sales Management */}
      <Route path="/superadmin/sales/orders" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <SalesOrders language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/sales/quotations" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <Quotations language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/sales/pipeline" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <SalesPipeline language={language} />
        </RoleBasedRoute>
      } />
      {/* FIXED: Moved customers to correct superadmin/sales path */}
      <Route path="/superadmin/sales/customers" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <Customers language={language} />
        </RoleBasedRoute>
      } />

      {/* CRM & Products */}
      <Route path="/admin/customers" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Customers language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/products" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Products language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/inventory" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Inventory language={language} />
        </RoleBasedRoute>
      } />

      {/* Analytics */}
      <Route path="/admin/analytics-old" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <AnalyticsComponent language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/business-intelligence" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <UnifiedKPIDashboard language={language} defaultType="business-intelligence" />
        </RoleBasedRoute>
      } />
      <Route path="/admin/advanced-dashboard" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <AdvancedDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* Unified KPI Dashboard - Consolidates all KPI dashboard types */}
      <Route path="/admin/kpi/dashboard" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <UnifiedKPIDashboard language={language} defaultType="general" />
        </RoleBasedRoute>
      } />
      <Route path="/admin/kpi/management" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <KPIManagement language={language} />
        </RoleBasedRoute>
      } />

      {/* Enhanced KPI Analytics for Super Admin - Now using Unified Dashboard */}
      <Route path="/superadmin/kpi/executive" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <UnifiedKPIDashboard language={language} defaultType="executive" />
        </RoleBasedRoute>
      } />

      {/* Backward Compatibility Redirects for Old KPI Routes */}
      <Route path="/admin/kpi/executive" element={
        <Navigate to="/admin/kpi/dashboard?type=executive" replace />
      } />
      <Route path="/admin/kpi/business-intelligence" element={
        <Navigate to="/admin/business-intelligence" replace />
      } />
      <Route path="/admin/kpi/general" element={
        <Navigate to="/admin/kpi/dashboard" replace />
      } />
      <Route path="/superadmin/kpi/hr" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <HRAnalyticsDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/kpi/financial" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <FinancialAnalyticsDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/kpi/department" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <DepartmentAnalyticsDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/kpi/project" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <ProjectAnalyticsDashboard />
        </RoleBasedRoute>
      } />

      {/* Super Admin Specific KPI Routes */}
      <Route path="/superadmin/kpi/organization" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <ExecutiveKPIDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/kpi/strategic" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <ExecutiveKPIDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/kpi/cross-department" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <DepartmentAnalyticsDashboard />
        </RoleBasedRoute>
      } />

      {/* HR KPI Sub-routes for Super Admin */}
      <Route path="/superadmin/kpi/hr/performance" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <HRAnalyticsDashboard kpiType="performance" />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/kpi/hr/retention" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <HRAnalyticsDashboard kpiType="retention" />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/kpi/hr/attendance" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <HRAnalyticsDashboard kpiType="attendance" />
        </RoleBasedRoute>
      } />

      {/* Financial KPI Sub-routes for Super Admin */}
      <Route path="/superadmin/kpi/financial/revenue" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <FinancialAnalyticsDashboard kpiType="revenue" />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/kpi/financial/profitability" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <FinancialAnalyticsDashboard kpiType="profitability" />
        </RoleBasedRoute>
      } />
      <Route path="/superadmin/kpi/financial/cashflow" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/superadmin/unauthorized">
          <FinancialAnalyticsDashboard kpiType="cashflow" />
        </RoleBasedRoute>
      } />

      {/* Advanced Features */}
      <Route path="/admin/workflows" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <WorkflowAutomation language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/report-generator" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <ReportGenerator language={language} />
        </RoleBasedRoute>
      } />


      {/* Enterprise Features */}
      <Route path="/admin/enterprise/ml" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <MLDashboard language={language} userRole="super_admin" />
        </RoleBasedRoute>
      } />
      {/* TODO: Uncomment when components are created
      <Route path="/admin/enterprise/automation" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <AutomationDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/enterprise/tenant" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <TenantDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/enterprise/compliance" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <ComplianceDashboard language={language} />
        </RoleBasedRoute>
      } />
      */}

      {/* Personal Features */}
      <Route path="/admin/profile" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <PersonalProfile language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/messages" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <PersonalMessages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/calendar" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <PersonalCalendar language={language} />
        </RoleBasedRoute>
      } />

      {/* Communication */}
      <Route path="/admin/communication/messages" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Messages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/communication/announcements" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Announcements language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/communication/documents" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Documents language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/communication/meetings" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Meetings language={language} />
        </RoleBasedRoute>
      } />

      {/* System Administration */}
      <Route path="/admin/users" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <UserManagement language={language} />
        </RoleBasedRoute>
      } />

      {/* Enhanced Super Admin Features */}
      <Route path="/admin/system" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <SuperAdminSystemSettings language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/security" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <SuperAdminSecurityCenter language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/ai" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <AIManagement language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/analytics" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <AdvancedAnalytics language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/compliance" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <ComplianceCenter language={language} />
        </RoleBasedRoute>
      } />

      {/* Vendor Management */}
      <Route path="/admin/vendors" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <VendorManagement language={language} />
        </RoleBasedRoute>
      } />

      {/* Unauthorized Access */}
      <Route path="/superadmin/unauthorized" element={<Unauthorized language={language} />} />

      {/* Catch all - redirect to superadmin dashboard */}
      <Route path="*" element={<Navigate to="/superadmin/dashboard" replace />} />
      </Routes>
    </LazyErrorBoundary>
  )
}
