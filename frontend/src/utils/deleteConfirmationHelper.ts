/**
 * Delete Confirmation Helper
 * Utility functions for standardized delete confirmation across CRUD operations
 */

import { useState } from 'react'

export interface DeleteConfirmationState<T> {
  showDeleteModal: boolean
  itemToDelete: T | null
}

export interface DeleteConfirmationActions<T> {
  setShowDeleteModal: (show: boolean) => void
  setItemToDelete: (item: T | null) => void
  handleDeleteClick: (item: T) => void
  handleDeleteConfirm: () => Promise<void>
  handleDeleteCancel: () => void
}

export interface UseDeleteConfirmationOptions<T> {
  deleteItem: (id: string | number) => Promise<void>
  onDeleteSuccess?: () => void
  onDeleteError?: (error: Error) => void
}

/**
 * Custom hook for managing delete confirmation state and actions
 */
export function useDeleteConfirmation<T extends { id: string | number }>(
  options: UseDeleteConfirmationOptions<T>
): [DeleteConfirmationState<T>, DeleteConfirmationActions<T>] {
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<T | null>(null)

  const handleDeleteClick = (item: T) => {
    setItemToDelete(item)
    setShowDeleteModal(true)
  }

  const handleDeleteConfirm = async () => {
    if (itemToDelete) {
      try {
        await options.deleteItem(itemToDelete.id)
        setShowDeleteModal(false)
        setItemToDelete(null)
        options.onDeleteSuccess?.()
      } catch (error) {
        console.error('Delete error:', error)
        options.onDeleteError?.(error as Error)
        // Keep modal open on error so user can retry
      }
    }
  }

  const handleDeleteCancel = () => {
    setShowDeleteModal(false)
    setItemToDelete(null)
  }

  const state: DeleteConfirmationState<T> = {
    showDeleteModal,
    itemToDelete
  }

  const actions: DeleteConfirmationActions<T> = {
    setShowDeleteModal,
    setItemToDelete,
    handleDeleteClick,
    handleDeleteConfirm,
    handleDeleteCancel
  }

  return [state, actions]
}

/**
 * Generate delete confirmation message based on item type and language
 */
export function getDeleteConfirmationMessage(
  itemType: string,
  language: 'ar' | 'en',
  itemName?: string
): string {
  const messages = {
    ar: {
      order: 'هل أنت متأكد من حذف هذا الأمر؟',
      customer: 'هل أنت متأكد من حذف هذا العميل؟',
      product: 'هل أنت متأكد من حذف هذا المنتج؟',
      employee: 'هل أنت متأكد من حذف هذا الموظف؟',
      project: 'هل أنت متأكد من حذف هذا المشروع؟',
      task: 'هل أنت متأكد من حذف هذه المهمة؟',
      default: 'هل أنت متأكد من حذف هذا العنصر؟'
    },
    en: {
      order: 'Are you sure you want to delete this order?',
      customer: 'Are you sure you want to delete this customer?',
      product: 'Are you sure you want to delete this product?',
      employee: 'Are you sure you want to delete this employee?',
      project: 'Are you sure you want to delete this project?',
      task: 'Are you sure you want to delete this task?',
      default: 'Are you sure you want to delete this item?'
    }
  }

  const message = messages[language][itemType as keyof typeof messages[typeof language]] || 
                  messages[language].default

  return itemName ? `${message} (${itemName})` : message
}

/**
 * Generate delete confirmation title based on language
 */
export function getDeleteConfirmationTitle(language: 'ar' | 'en'): string {
  return language === 'ar' ? 'تأكيد الحذف' : 'Confirm Delete'
}

/**
 * Standard delete action configuration for CRUD tables
 */
export function createDeleteAction<T extends { id: string | number }>(
  handleDeleteClick: (item: T) => void,
  language: 'ar' | 'en'
) {
  const t = {
    ar: { delete: 'حذف' },
    en: { delete: 'Delete' }
  }

  return {
    label: t[language].delete,
    icon: 'Trash2', // Will be imported as Trash2 from lucide-react
    onClick: handleDeleteClick,
    variant: 'ghost' as const,
    className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
  }
}
