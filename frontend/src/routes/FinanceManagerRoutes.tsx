import { Routes, Route, Navigate } from 'react-router-dom'
import RoleBasedRoute from '../components/RoleBasedRoute'
import Unauthorized from '../pages/Unauthorized'

// Finance Manager Dashboard
import FinanceManagerDashboard from '../pages/dashboards/FinanceManagerDashboard'

// Security Components (tested and working)
import SecurityDashboard from '../pages/security/SecurityDashboard'
import SecurityIncidents from '../pages/security/SecurityIncidents'
import ComplianceManagement from '../pages/security/ComplianceManagement'

// Enhanced KPI Dashboards - now using HierarchicalKPIDashboard
import { KPIDashboard } from './lazyRoutes'

interface FinanceManagerRoutesProps {
  language: 'ar' | 'en'
}

export default function FinanceManagerRoutes({ language }: FinanceManagerRoutesProps) {
  return (
    <Routes>
      {/* Dashboard */}
      <Route path="/" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <FinanceManagerDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/dashboard" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <FinanceManagerDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* Security & Compliance - Working components */}
      <Route path="/finance/security" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <SecurityDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/security/dashboard" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <SecurityDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/security/incidents" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <SecurityIncidents language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/security/compliance" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <ComplianceManagement language={language} />
        </RoleBasedRoute>
      } />

      {/* Enhanced Financial KPI Analytics - Each with specific KPI type */}
      <Route path="/finance/kpi/revenue" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <KPIDashboard dashboardType="financial" kpiFilters={{ kpiType: "revenue" }} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/kpi/profitability" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <KPIDashboard dashboardType="financial" kpiFilters={{ kpiType: "profitability" }} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/kpi/cashflow" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <KPIDashboard dashboardType="financial" kpiFilters={{ kpiType: "cashflow" }} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/kpi/budget" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <KPIDashboard dashboardType="financial" kpiFilters={{ kpiType: "budget" }} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/kpi/costs" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <KPIDashboard dashboardType="financial" kpiFilters={{ kpiType: "costs" }} />
        </RoleBasedRoute>
      } />

      {/* Unauthorized Access */}
      <Route path="/finance/unauthorized" element={<Unauthorized language={language} />} />

      {/* Catch all - redirect to finance dashboard */}
      <Route path="*" element={<Navigate to="/finance/dashboard" replace />} />
    </Routes>
  )
}
