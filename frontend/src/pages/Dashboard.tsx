import { useState, useEffect, memo, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { apiClient } from '@/services/api'
import {
  Users, Building, TrendingUp, DollarSign, BarChart3, Calendar, Bell, Settings,
  Target, Briefcase, FileText, MessageSquare, Zap, Award, TrendingDown, Eye,
  Download, RefreshCw, Filter, Clock, CheckCircle, AlertTriangle, Activity,
  PieChart, LineChart, ArrowUpRight, ArrowDownRight, Plus, Search
} from 'lucide-react'

interface DashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    dashboard: 'لوحة التحكم',
    welcome: 'مرحباً بك في نمو',
    overview: 'نظرة عامة',
    totalEmployees: 'إجمالي الموظفين',
    departments: 'الأقسام',
    monthlyRevenue: 'الإيرادات الشهرية',
    growthRate: 'معدل النمو',
    recentActivities: 'الأنشطة الحديثة',
    quickActions: 'إجراءات سريعة',
    viewReports: 'عرض التقارير',
    manageUsers: 'إدارة المستخدمين',
    scheduleEvent: 'جدولة حدث',
    systemSettings: 'إعدادات النظام',
    notifications: 'الإشعارات',
    newEmployeeAdded: 'تم إضافة موظف جديد',
    reportGenerated: 'تم إنشاء تقرير جديد',
    meetingScheduled: 'تم جدولة اجتماع',
    systemUpdate: 'تحديث النظام',
    // Advanced Features
    activeProjects: 'المشاريع النشطة',
    pendingTasks: 'المهام المعلقة',
    totalBudget: 'إجمالي الميزانية',
    monthlyExpenses: 'المصروفات الشهرية',
    teamPerformance: 'أداء الفريق',
    systemHealth: 'صحة النظام',
    realTimeAnalytics: 'التحليلات المباشرة',
    kpiDashboard: 'لوحة مؤشرات الأداء',
    projectProgress: 'تقدم المشاريع',
    financialOverview: 'النظرة المالية',
    progress: 'التقدم',
    hrMetrics: 'مقاييس الموارد البشرية',
    operationalInsights: 'رؤى تشغيلية',
    refresh: 'تحديث',
    export: 'تصدير',
    filter: 'تصفية',
    viewAll: 'عرض الكل',
    addNew: 'إضافة جديد',
    search: 'بحث',
    settings: 'الإعدادات',
    help: 'المساعدة',
    profile: 'الملف الشخصي',
    logout: 'تسجيل الخروج',
    // Status indicators
    online: 'متصل',
    offline: 'غير متصل',
    active: 'نشط',
    inactive: 'غير نشط',
    completed: 'مكتمل',
    inProgress: 'قيد التنفيذ',
    pending: 'معلق',
    overdue: 'متأخر',
    // Time periods
    today: 'اليوم',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',
    thisYear: 'هذا العام',
    lastMonth: 'الشهر الماضي',
    lastYear: 'العام الماضي'
  },
  en: {
    dashboard: 'Dashboard',
    welcome: 'Welcome to Numu',
    overview: 'Overview',
    totalEmployees: 'Total Employees',
    departments: 'Departments',
    monthlyRevenue: 'Monthly Revenue',
    growthRate: 'Growth Rate',
    recentActivities: 'Recent Activities',
    quickActions: 'Quick Actions',
    viewReports: 'View Reports',
    manageUsers: 'Manage Users',
    scheduleEvent: 'Schedule Event',
    systemSettings: 'System Settings',
    notifications: 'Notifications',
    newEmployeeAdded: 'New employee added',
    reportGenerated: 'Report generated',
    meetingScheduled: 'Meeting scheduled',
    systemUpdate: 'System update',
    // Advanced Features
    activeProjects: 'Active Projects',
    pendingTasks: 'Pending Tasks',
    totalBudget: 'Total Budget',
    monthlyExpenses: 'Monthly Expenses',
    teamPerformance: 'Team Performance',
    systemHealth: 'System Health',
    realTimeAnalytics: 'Real-time Analytics',
    kpiDashboard: 'KPI Dashboard',
    projectProgress: 'Project Progress',
    financialOverview: 'Financial Overview',
    progress: 'Progress',
    hrMetrics: 'HR Metrics',
    operationalInsights: 'Operational Insights',
    refresh: 'Refresh',
    export: 'Export',
    filter: 'Filter',
    viewAll: 'View All',
    addNew: 'Add New',
    search: 'Search',
    settings: 'Settings',
    help: 'Help',
    profile: 'Profile',
    logout: 'Logout',
    // Status indicators
    online: 'Online',
    offline: 'Offline',
    active: 'Active',
    inactive: 'Inactive',
    completed: 'Completed',
    inProgress: 'In Progress',
    pending: 'Pending',
    overdue: 'Overdue',
    // Time periods
    today: 'Today',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisYear: 'This Year',
    lastMonth: 'Last Month',
    lastYear: 'Last Year'
  }
}

const Dashboard = memo(function Dashboard({ language }: DashboardProps) {
  const [refreshing, setRefreshing] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState('thisMonth')
  const [realTimeData, setRealTimeData] = useState({
    onlineUsers: 0,
    systemLoad: 0,
    activeConnections: 0
  })
  const [dashboardStats, setDashboardStats] = useState({
    total_employees: 0,
    total_departments: 0,
    active_projects: 0,
    pending_tasks: 0,
    pending_leave_requests: 0
  })
  const [projectProgress, setProjectProgress] = useState<Array<{
    name: string
    progress: number
    status: string
    dueDate: string
  }>>([])

  const t = useMemo(() => translations[language], [language])
  const isRTL = useMemo(() => language === 'ar', [language])

  // Load real KPI data from API
  const loadKPIData = async () => {
    try {
      console.log('🔄 Loading real KPI data for dashboard...')

      // Fetch KPI metrics from API
      const response = await apiClient.get('/kpi-metrics/', {
        params: {
          category_filter: 'Financial,Performance,Budget',
          limit: 10
        }
      })

      const kpiMetrics = response.data.results || response.data || []
      console.log('📊 KPI metrics loaded:', kpiMetrics.length, 'metrics')

      // Find specific KPIs for Budget and Performance
      const budgetKPI = kpiMetrics.find(kpi =>
        kpi.name?.toLowerCase().includes('budget') ||
        kpi.name?.toLowerCase().includes('ميزانية') ||
        kpi.metric_type === 'FINANCIAL'
      )

      const performanceKPI = kpiMetrics.find(kpi =>
        kpi.name?.toLowerCase().includes('performance') ||
        kpi.name?.toLowerCase().includes('أداء') ||
        kpi.metric_type === 'PERFORMANCE'
      )

      // Update KPI data with real values
      setKpiData(prev => prev.map(kpi => {
        if (kpi.name.includes('Budget') || kpi.name.includes('الميزانية')) {
          const value = budgetKPI?.current_value?.value || budgetKPI?.value || 0
          const target = budgetKPI?.target_value || value * 1.2 || 3.0
          return {
            ...kpi,
            value: value,
            target: target,
            percentage: target > 0 ? Math.min((value / target) * 100, 100) : 0
          }
        }
        if (kpi.name.includes('Performance') || kpi.name.includes('الأداء')) {
          const value = performanceKPI?.current_value?.value || performanceKPI?.value || 0
          const target = performanceKPI?.target_value || value * 1.05 || 95.0
          return {
            ...kpi,
            value: value,
            target: target,
            percentage: target > 0 ? Math.min((value / target) * 100, 100) : 0
          }
        }
        return kpi
      }))

      console.log('✅ KPI data updated with real values')

    } catch (error) {
      console.error('❌ Error loading KPI data:', error)
      // Keep default values if API fails
    }
  }

  // Load real-time data and dashboard stats from API
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        // Import APIs
        const { dashboardAPI } = await import('../services/api')
        const { projectAPI } = await import('../services/projectAPI')

        // Load dashboard stats and projects in parallel
        const [apiStats, projects] = await Promise.all([
          dashboardAPI.getStats(),
          projectAPI.getAll({ limit: 4 }) // Get top 4 projects for dashboard
        ])

        // Update dashboard stats
        setDashboardStats(apiStats)

        // Update real-time data with actual values from API
        setRealTimeData({
          onlineUsers: apiStats.active_employees || 0,
          systemLoad: apiStats.system_health?.cpu_usage || 0,
          activeConnections: apiStats.active_employees || 0
        })

        // Update project progress with real data
        const projectProgressData = projects.map(project => ({
          name: language === 'ar' ? (project.name_ar || project.name) : project.name,
          progress: project.progress_percentage,
          status: project.status.toLowerCase(),
          dueDate: project.end_date
        }))
        setProjectProgress(projectProgressData)

        // Load real KPI data for Budget and Performance
        await loadKPIData()

      } catch (error) {
        console.error('Error loading dashboard data:', error)
        // Keep default values if API fails
      }
    }

    loadDashboardData()

    // Update every 30 seconds with real data
    const interval = setInterval(loadDashboardData, 30000)
    return () => clearInterval(interval)
  }, [language])

  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }, [])

  // Enhanced stats with real API data and trends
  const stats = [
    {
      title: t.totalEmployees,
      value: dashboardStats.total_employees.toLocaleString(),
      change: null, // TODO: Calculate from historical data
      changePercent: null, // TODO: Calculate from historical data
      trend: 'stable',
      icon: Users,
      color: 'text-blue-400',
      bgGradient: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeProjects,
      value: dashboardStats.active_projects.toString(),
      change: null, // TODO: Calculate from historical data
      changePercent: null, // TODO: Calculate from historical data
      trend: 'stable',
      icon: Briefcase,
      color: 'text-green-400',
      bgGradient: 'from-green-500 to-green-600'
    },
    {
      title: t.totalBudget,
      value: dashboardStats.monthly_expenses ? `$${dashboardStats.monthly_expenses.toLocaleString()}` : '$0',
      change: null, // TODO: Calculate from historical data
      changePercent: null, // TODO: Calculate from historical data
      trend: 'stable',
      icon: DollarSign,
      color: 'text-purple-400',
      bgGradient: 'from-purple-500 to-purple-600'
    },
    {
      title: t.teamPerformance,
      value: '0%', // TODO: Connect to performance API
      change: null, // TODO: Calculate from historical data
      changePercent: null, // TODO: Calculate from historical data
      trend: 'stable',
      icon: TrendingUp,
      color: 'text-orange-400',
      bgGradient: 'from-orange-500 to-orange-600'
    },
    {
      title: t.pendingTasks,
      value: dashboardStats.pending_tasks.toString(),
      change: null, // TODO: Calculate from historical data
      changePercent: null, // TODO: Calculate from historical data
      trend: 'stable',
      icon: CheckCircle,
      color: 'text-cyan-400',
      bgGradient: 'from-cyan-500 to-cyan-600'
    },
    {
      title: t.systemHealth,
      value: `${realTimeData.systemLoad}%`,
      change: realTimeData.systemLoad > 70 ? 'High' : 'Normal',
      changePercent: t.online,
      trend: realTimeData.systemLoad > 70 ? 'up' : 'stable',
      icon: Activity,
      color: 'text-emerald-400',
      bgGradient: 'from-emerald-500 to-emerald-600'
    }
  ]

  // Enhanced quick actions with more functionality
  const quickActions = [
    { title: t.addNew, icon: Plus, color: 'from-blue-500 to-blue-600', href: '/employees' },
    { title: t.viewReports, icon: BarChart3, color: 'from-green-500 to-green-600', href: '/reports' },
    { title: t.manageUsers, icon: Users, color: 'from-purple-500 to-purple-600', href: '/employees' },
    { title: t.scheduleEvent, icon: Calendar, color: 'from-orange-500 to-orange-600', href: '/communication/meetings' },
    { title: t.systemSettings, icon: Settings, color: 'from-pink-500 to-pink-600', href: '/settings' },
    { title: t.search, icon: Search, color: 'from-cyan-500 to-cyan-600', href: '#' }
  ]

  // Real-time activities with enhanced data
  const activities = [
    {
      title: t.newEmployeeAdded,
      time: '2 ساعات مضت',
      icon: Users,
      type: 'success',
      details: 'أحمد محمد - قسم تقنية المعلومات'
    },
    {
      title: t.reportGenerated,
      time: '4 ساعات مضت',
      icon: FileText,
      type: 'info',
      details: 'تقرير الأداء الشهري'
    },
    {
      title: t.meetingScheduled,
      time: '6 ساعات مضت',
      icon: Calendar,
      type: 'warning',
      details: 'اجتماع مراجعة المشروع - غداً 10:00 ص'
    },
    {
      title: t.systemUpdate,
      time: '1 يوم مضى',
      icon: Zap,
      type: 'success',
      details: 'تحديث النظام v2.1.0'
    },
    {
      title: 'طلب إجازة جديد',
      time: '3 ساعات مضت',
      icon: Clock,
      type: 'pending',
      details: 'فاطمة علي - إجازة سنوية'
    }
  ]

  // KPI data for charts using real API data with dynamic targets
  const [kpiData, setKpiData] = useState([
    {
      name: language === 'ar' ? 'الموظفين' : 'Employees',
      value: dashboardStats.total_employees,
      target: Math.max(dashboardStats.total_employees * 1.2, 100), // Dynamic target: 20% above current or minimum 100
      percentage: dashboardStats.total_employees > 0 ? Math.min((dashboardStats.total_employees / Math.max(dashboardStats.total_employees * 1.2, 100)) * 100, 100) : 0
    },
    {
      name: language === 'ar' ? 'المشاريع' : 'Projects',
      value: dashboardStats.active_projects,
      target: Math.max(dashboardStats.active_projects * 1.5, 10), // Dynamic target: 50% above current or minimum 10
      percentage: dashboardStats.active_projects > 0 ? Math.min((dashboardStats.active_projects / Math.max(dashboardStats.active_projects * 1.5, 10)) * 100, 100) : 0
    },
    {
      name: language === 'ar' ? 'الميزانية' : 'Budget',
      value: 0, // Will be loaded from KPI API
      target: 0, // Will be loaded from KPI API
      percentage: 0 // Will be calculated from real budget data
    },
    {
      name: language === 'ar' ? 'الأداء' : 'Performance',
      value: 0, // Will be loaded from KPI API
      target: 0, // Will be loaded from KPI API
      percentage: 0 // Will be calculated from real performance data
    }
  ])

  // Project progress data is now loaded from API in useEffect

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUpRight className="h-4 w-4 text-green-400" />
      case 'down':
        return <ArrowDownRight className="h-4 w-4 text-red-400" />
      default:
        return <Activity className="h-4 w-4 text-blue-400" />
    }
  }

  const getActivityTypeColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'from-green-500 to-green-600'
      case 'warning':
        return 'from-yellow-500 to-yellow-600'
      case 'info':
        return 'from-blue-500 to-blue-600'
      case 'pending':
        return 'from-orange-500 to-orange-600'
      default:
        return 'from-gray-500 to-gray-600'
    }
  }

  return (
    <div className={`dashboard-container w-full max-w-none min-w-0 space-y-6 ${isRTL ? 'rtl' : 'ltr'}`} style={{ width: '100%', maxWidth: 'none' }}>
      {/* Enhanced Header with Real-time Indicators */}
      <div className="dashboard-title w-full flex flex-col sm:flex-row sm:items-start lg:items-center justify-between gap-4 sm:gap-6 mb-6 sm:mb-8" style={{ width: '100%', maxWidth: 'none' }}>
        <div className="flex-1 w-full">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-2">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">
              {t.dashboard}
            </h1>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 sm:w-3 sm:h-3 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-400 text-xs sm:text-sm font-medium">{t.online}</span>
            </div>
          </div>
          <p className="text-white/80 text-sm sm:text-base lg:text-lg mb-2 sm:mb-3">
            {t.welcome}
          </p>
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 lg:gap-6 text-xs sm:text-sm text-white/60">
            <div className="flex items-center gap-2">
              <Users className="h-3 w-3 sm:h-4 sm:w-4" />
              <span>{realTimeData.onlineUsers} مستخدم متصل</span>
            </div>
            <div className="flex items-center gap-2">
              <Activity className="h-3 w-3 sm:h-4 sm:w-4" />
              <span>{realTimeData.activeConnections} اتصال نشط</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-3 w-3 sm:h-4 sm:w-4" />
              <span>حمولة النظام: {realTimeData.systemLoad}%</span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap items-center gap-2 sm:gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button text-xs sm:text-sm px-3 sm:px-4 py-2"
          >
            <RefreshCw className={`h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline">{t.refresh}</span>
          </Button>
          <Button variant="outline" className="glass-button text-xs sm:text-sm px-3 sm:px-4 py-2">
            <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">{t.export}</span>
          </Button>
          <Button variant="outline" className="glass-button text-xs sm:text-sm px-3 sm:px-4 py-2">
            <Filter className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">{t.filter}</span>
          </Button>
          <div className="relative glass-card p-2 sm:p-3 cursor-pointer hover:scale-105 transition-all">
            <Bell className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            <div className="absolute -top-1 -right-1 w-2 h-2 sm:w-3 sm:h-3 bg-red-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Grid with Trends */}
      <div className="dashboard-grid w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 sm:gap-6" style={{ width: '100%', maxWidth: 'none' }}>
        {stats.map((stat, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 hover:scale-105 transition-all duration-300">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-start justify-between mb-3 sm:mb-4">
                <div className="flex-1">
                  <p className="text-xs sm:text-sm font-medium text-white/70 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-lg sm:text-xl lg:text-2xl font-bold text-white">
                    {stat.value}
                  </p>
                </div>
                <div className={`p-2 sm:p-3 rounded-lg sm:rounded-xl bg-gradient-to-r ${stat.bgGradient} shadow-lg`}>
                  <stat.icon className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-white" />
                </div>
              </div>

              {/* Trend Indicator */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1 sm:gap-2">
                  {getTrendIcon(stat.trend)}
                  <span className={`text-xs sm:text-sm font-medium ${
                    stat.trend === 'up' ? 'text-green-400' :
                    stat.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                  }`}>
                    {stat.changePercent}
                  </span>
                </div>
                <span className="text-xs text-white/60">
                  {stat.change}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="dashboard-grid w-full grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Enhanced Quick Actions */}
        <Card className="lg:col-span-2 glass-card border-white/20">
          <CardHeader className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4">
              <CardTitle className="text-white text-lg sm:text-xl">{t.quickActions}</CardTitle>
              <Button variant="outline" size="sm" className="glass-button text-xs sm:text-sm px-3 py-2">
                <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">{t.viewAll}</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 gap-3 sm:gap-4">
              {quickActions.map((action, index) => (
                <div
                  key={index}
                  className="group flex flex-col items-center gap-2 sm:gap-3 p-3 sm:p-4 lg:p-6 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
                >
                  <div className={`p-2 sm:p-3 lg:p-4 rounded-xl sm:rounded-2xl bg-gradient-to-r ${action.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <action.icon className="h-5 w-5 sm:h-6 sm:w-6 lg:h-8 lg:w-8 text-white" />
                  </div>
                  <span className="font-medium text-white text-center text-xs sm:text-sm">
                    {action.title}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Recent Activities */}
        <Card className="glass-card border-white/20">
          <CardHeader className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4">
              <CardTitle className="text-white text-lg sm:text-xl">{t.recentActivities}</CardTitle>
              <Button variant="outline" size="sm" className="glass-button text-xs sm:text-sm px-3 py-2">
                <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">{t.viewAll}</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <div className="space-y-2 sm:space-y-3">
              {activities.map((activity, index) => (
                <div key={index} className="flex items-start gap-2 sm:gap-3 p-3 sm:p-4 glass-card border-white/10 hover:border-white/30 transition-all duration-300">
                  <div className={`p-2 sm:p-3 rounded-lg sm:rounded-xl bg-gradient-to-r ${getActivityTypeColor(activity.type)} shadow-lg`}>
                    <activity.icon className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs sm:text-sm font-medium text-white mb-1">
                      {activity.title}
                    </p>
                    <p className="text-xs text-white/60 mb-1 sm:mb-2">
                      {activity.details}
                    </p>
                    <p className="text-xs text-white/40">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Analytics Section */}
      <div className="dashboard-grid w-full grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* KPI Dashboard */}
        <Card className="glass-card border-white/20">
          <CardHeader className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4">
              <CardTitle className="text-white text-lg sm:text-xl">{t.kpiDashboard}</CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="glass-button text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2">
                  <PieChart className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Chart</span>
                </Button>
                <Button variant="outline" size="sm" className="glass-button text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2">
                  <LineChart className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Trend</span>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <div className="space-y-4 sm:space-y-6">
              {kpiData.map((kpi, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-white/80 font-medium text-sm sm:text-base">{kpi.name}</span>
                    <div className="flex items-center gap-1 sm:gap-2">
                      <span className="text-white font-bold text-sm sm:text-base">{kpi.value}</span>
                      <span className="text-white/60 text-xs sm:text-sm">/ {kpi.target}</span>
                    </div>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2 sm:h-3">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-green-500 h-2 sm:h-3 rounded-full transition-all duration-1000"
                      style={{ width: `${kpi.percentage}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-white/60">
                    <span>Progress: {kpi.percentage}%</span>
                    <span className={kpi.percentage >= 90 ? 'text-green-400' : kpi.percentage >= 70 ? 'text-yellow-400' : 'text-red-400'}>
                      {kpi.percentage >= 90 ? 'Excellent' : kpi.percentage >= 70 ? 'Good' : 'Needs Attention'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Project Progress */}
        <Card className="glass-card border-white/20">
          <CardHeader className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4">
              <CardTitle className="text-white text-lg sm:text-xl">{t.projectProgress}</CardTitle>
              <Button variant="outline" size="sm" className="glass-button text-xs sm:text-sm px-3 py-2">
                <Target className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">{t.viewAll}</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <div className="space-y-3 sm:space-y-4">
              {projectProgress.map((project, index) => (
                <div key={index} className="p-3 sm:p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-3 mb-2 sm:mb-3">
                    <h4 className="text-white font-medium text-xs sm:text-sm">{project.name}</h4>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium self-start sm:self-auto ${
                      project.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                      project.status === 'inProgress' ? 'bg-blue-500/20 text-blue-400' :
                      'bg-gray-500/20 text-gray-400'
                    }`}>
                      {project.status === 'completed' ? t.completed :
                       project.status === 'inProgress' ? t.inProgress : t.pending}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs sm:text-sm">
                      <span className="text-white/70">{t.progress}</span>
                      <span className="text-white font-medium">{project.progress}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-1000 ${
                          project.progress === 100 ? 'bg-gradient-to-r from-green-500 to-green-600' :
                          project.progress >= 70 ? 'bg-gradient-to-r from-blue-500 to-green-500' :
                          'bg-gradient-to-r from-orange-500 to-yellow-500'
                        }`}
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0 text-xs text-white/60">
                      <span>Due: {project.dueDate}</span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(project.dueDate) > new Date() ? 'On Track' : 'Overdue'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Real-time System Status */}
      <Card className="glass-card border-white/20">
        <CardHeader className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4">
            <CardTitle className="text-white text-lg sm:text-xl">{t.systemHealth}</CardTitle>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 sm:w-3 sm:h-3 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-400 text-xs sm:text-sm font-medium">{t.online}</span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <div className="dashboard-grid w-full grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
            <div className="text-center p-3 sm:p-4 glass-card border-white/10 rounded-lg">
              <div className="text-2xl sm:text-3xl font-bold text-blue-400 mb-1 sm:mb-2">{realTimeData.onlineUsers}</div>
              <div className="text-white/70 text-xs sm:text-sm">Active Users</div>
              <div className="text-green-400 text-xs">+5% from yesterday</div>
            </div>
            <div className="text-center p-3 sm:p-4 glass-card border-white/10 rounded-lg">
              <div className="text-2xl sm:text-3xl font-bold text-purple-400 mb-1 sm:mb-2">{realTimeData.systemLoad}%</div>
              <div className="text-white/70 text-xs sm:text-sm">System Load</div>
              <div className={`text-xs ${realTimeData.systemLoad > 80 ? 'text-red-400' : 'text-green-400'}`}>
                {realTimeData.systemLoad > 80 ? 'High Load' : 'Normal'}
              </div>
            </div>
            <div className="text-center p-3 sm:p-4 glass-card border-white/10 rounded-lg">
              <div className="text-2xl sm:text-3xl font-bold text-orange-400 mb-1 sm:mb-2">{realTimeData.activeConnections.toLocaleString()}</div>
              <div className="text-white/70 text-xs sm:text-sm">Connections</div>
              <div className="text-blue-400 text-xs">Real-time</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
})

export default Dashboard
