"""
Management command to set up automated KPI system with real-time calculation
from operational data following enterprise BI best practices.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
from ems.models import (
    KPICategory, KPI, Employee, Department
)
# KPI calculation engine import removed - using direct model operations


class Command(BaseCommand):
    help = 'Set up automated KPI system with real-time calculation from operational data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--replace-existing',
            action='store_true',
            help='Replace existing KPIs with automated ones'
        )
        parser.add_argument(
            '--calculate-now',
            action='store_true',
            help='Calculate all KPI values immediately after setup'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up automated KPI system...'))
        
        if options['replace_existing']:
            # Clear existing KPIs
            KPI.objects.all().delete()
            self.stdout.write(self.style.WARNING('Cleared existing KPIs'))
        
        # Get admin user
        admin_user = User.objects.filter(username='admin').first()
        if not admin_user:
            self.stdout.write(self.style.ERROR('Admin user not found'))
            return
            
        admin_employee = Employee.objects.filter(user=admin_user).first()
        if not admin_employee:
            self.stdout.write(self.style.ERROR('Admin employee not found'))
            return

        # Create KPI categories
        self.create_kpi_categories()
        
        # Create automated KPIs
        self.create_automated_kpis(admin_employee)
        
        # Calculate initial values if requested
        if options['calculate_now']:
            self.calculate_initial_values()
        
        self.stdout.write(self.style.SUCCESS('Automated KPI system setup completed!'))

    def create_kpi_categories(self):
        """Create KPI categories"""
        categories_data = [
            {
                'name': 'Human Resources',
                'name_ar': 'الموارد البشرية',
                'description': 'Employee and HR related metrics',
                'description_ar': 'مقاييس الموظفين والموارد البشرية',
                'color': '#3B82F6',
                'icon': 'users',
                'sort_order': 1
            },
            {
                'name': 'Financial',
                'name_ar': 'المالية',
                'description': 'Revenue, profit and financial metrics',
                'description_ar': 'الإيرادات والأرباح والمقاييس المالية',
                'color': '#10B981',
                'icon': 'dollar-sign',
                'sort_order': 2
            },
            {
                'name': 'Operations',
                'name_ar': 'العمليات',
                'description': 'Project and operational efficiency metrics',
                'description_ar': 'مقاييس كفاءة المشاريع والعمليات',
                'color': '#F59E0B',
                'icon': 'settings',
                'sort_order': 3
            },
            {
                'name': 'Customer',
                'name_ar': 'العملاء',
                'description': 'Customer satisfaction and retention metrics',
                'description_ar': 'مقاييس رضا العملاء والاحتفاظ بهم',
                'color': '#EF4444',
                'icon': 'heart',
                'sort_order': 4
            }
        ]

        for category_data in categories_data:
            category, created = KPICategory.objects.get_or_create(
                name=category_data['name'],
                defaults=category_data
            )
            if created:
                self.stdout.write(f"Created category: {category.name}")

    def create_automated_kpis(self, admin_employee):
        """Create automated KPIs that calculate from real operational data"""
        
        # Get categories
        hr_category = KPICategory.objects.get(name='Human Resources')
        financial_category = KPICategory.objects.get(name='Financial')
        operations_category = KPICategory.objects.get(name='Operations')
        customer_category = KPICategory.objects.get(name='Customer')

        automated_kpis_data = [
            # HR KPIs
            {
                'name': 'Employee Turnover Rate',
                'name_ar': 'معدل دوران الموظفين',
                'description': 'Percentage of employees who left the organization (calculated from Employee model)',
                'description_ar': 'نسبة الموظفين الذين تركوا المنظمة (محسوبة من نموذج الموظف)',
                'category': hr_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'DOWN',
                'target_value': 5.0,
                'warning_threshold': 8.0,
                'critical_threshold': 12.0,
                'calculation_method': 'EMPLOYEE_TURNOVER_RATE',
                'is_automated': True,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Average Attendance Rate',
                'name_ar': 'متوسط معدل الحضور',
                'description': 'Average employee attendance rate (calculated from Attendance model)',
                'description_ar': 'متوسط معدل حضور الموظفين (محسوب من نموذج الحضور)',
                'category': hr_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 95.0,
                'warning_threshold': 90.0,
                'critical_threshold': 85.0,
                'calculation_method': 'AVERAGE_ATTENDANCE_RATE',
                'is_automated': True,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Employee Productivity Score',
                'name_ar': 'درجة إنتاجية الموظف',
                'description': 'Employee productivity based on task completion (calculated from Task model)',
                'description_ar': 'إنتاجية الموظف بناءً على إنجاز المهام (محسوبة من نموذج المهمة)',
                'category': hr_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 85.0,
                'warning_threshold': 75.0,
                'critical_threshold': 65.0,
                'calculation_method': 'EMPLOYEE_PRODUCTIVITY_SCORE',
                'is_automated': True,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            
            # Financial KPIs
            {
                'name': 'Monthly Revenue',
                'name_ar': 'الإيرادات الشهرية',
                'description': 'Total revenue from paid customer invoices (calculated from CustomerInvoice model)',
                'description_ar': 'إجمالي الإيرادات من فواتير العملاء المدفوعة (محسوبة من نموذج فاتورة العميل)',
                'category': financial_category,
                'measurement_type': 'CURRENCY',
                'unit': 'SAR',
                'unit_ar': 'ريال سعودي',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 500000.0,
                'warning_threshold': 400000.0,
                'critical_threshold': 300000.0,
                'calculation_method': 'MONTHLY_REVENUE',
                'is_automated': True,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Profit Margin',
                'name_ar': 'هامش الربح',
                'description': 'Net profit margin percentage (calculated from CustomerInvoice and Expense models)',
                'description_ar': 'نسبة هامش الربح الصافي (محسوبة من نماذج فاتورة العميل والمصروفات)',
                'category': financial_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 25.0,
                'warning_threshold': 20.0,
                'critical_threshold': 15.0,
                'calculation_method': 'PROFIT_MARGIN',
                'is_automated': True,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Average Invoice Value',
                'name_ar': 'متوسط قيمة الفاتورة',
                'description': 'Average value of customer invoices (calculated from CustomerInvoice model)',
                'description_ar': 'متوسط قيمة فواتير العملاء (محسوبة من نموذج فاتورة العميل)',
                'category': financial_category,
                'measurement_type': 'CURRENCY',
                'unit': 'SAR',
                'unit_ar': 'ريال سعودي',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 15000.0,
                'warning_threshold': 12000.0,
                'critical_threshold': 10000.0,
                'calculation_method': 'AVERAGE_INVOICE_VALUE',
                'is_automated': True,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            
            # Operations KPIs
            {
                'name': 'Project Completion Rate',
                'name_ar': 'معدل إنجاز المشاريع',
                'description': 'Percentage of projects completed on time (calculated from Project model)',
                'description_ar': 'نسبة المشاريع المكتملة في الوقت المحدد (محسوبة من نموذج المشروع)',
                'category': operations_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 90.0,
                'warning_threshold': 80.0,
                'critical_threshold': 70.0,
                'calculation_method': 'PROJECT_COMPLETION_RATE',
                'is_automated': True,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Task Completion Rate',
                'name_ar': 'معدل إنجاز المهام',
                'description': 'Percentage of tasks completed on time (calculated from Task model)',
                'description_ar': 'نسبة المهام المكتملة في الوقت المحدد (محسوبة من نموذج المهمة)',
                'category': operations_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 85.0,
                'warning_threshold': 75.0,
                'critical_threshold': 65.0,
                'calculation_method': 'TASK_COMPLETION_RATE',
                'is_automated': True,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Average Project Duration',
                'name_ar': 'متوسط مدة المشروع',
                'description': 'Average time to complete projects in days (calculated from Project model)',
                'description_ar': 'متوسط الوقت لإكمال المشاريع بالأيام (محسوب من نموذج المشروع)',
                'category': operations_category,
                'measurement_type': 'TIME',
                'unit': 'Days',
                'unit_ar': 'يوم',
                'frequency': 'MONTHLY',
                'trend_direction': 'DOWN',
                'target_value': 60.0,
                'warning_threshold': 75.0,
                'critical_threshold': 90.0,
                'calculation_method': 'AVERAGE_PROJECT_DURATION',
                'is_automated': True,
                'status': 'ACTIVE',
                'created_by': admin_employee
            }
        ]

        created_kpis = []
        for kpi_data in automated_kpis_data:
            kpi, created = KPI.objects.get_or_create(
                name=kpi_data['name'],
                category=kpi_data['category'],
                defaults=kpi_data
            )
            if created:
                created_kpis.append(kpi)
                self.stdout.write(f"Created automated KPI: {kpi.name}")
            else:
                self.stdout.write(f"KPI already exists: {kpi.name}")

        self.stdout.write(f'Automated KPIs setup complete. Created: {len(created_kpis)}')

    def calculate_initial_values(self):
        """Calculate initial values for all automated KPIs"""
        self.stdout.write('Calculating initial KPI values...')
        
        engine = KPICalculationEngine()
        results = engine.calculate_all_kpis()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Initial calculation completed: {results["calculated"]} calculated, {results["failed"]} failed'
            )
        )
        
        if results['failed_kpis']:
            self.stdout.write(self.style.WARNING('Failed KPIs:'))
            for failed_kpi in results['failed_kpis']:
                self.stdout.write(f"  - {failed_kpi['name']} ({failed_kpi['calculation_method']})")
