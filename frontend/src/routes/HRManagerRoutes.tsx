import { Routes, Route, Navigate } from 'react-router-dom'
import RoleBasedRoute from '../components/RoleBasedRoute'
import Unauthorized from '../pages/Unauthorized'

// HR Manager Dashboard
import HRManagerDashboard from '../pages/dashboards/HRManagerDashboard'

// HR Manager Pages (HR-specific versions)
import HREmployees from '../pages/hr-specific/HREmployees'
import HRDepartments from '../pages/hr-specific/HRDepartments'
import HRReports from '../pages/hr-specific/HRReports'
import HRCustomers from '../pages/hr-specific/HRCustomers'

// HR Management (HR Manager can access all HR features)
import LeaveManagement from '../pages/hr/LeaveManagement'
import Attendance from '../pages/hr/Attendance'
import Performance from '../pages/hr/Performance'
import Payroll from '../pages/hr/Payroll'

// Communication (Available to all)
import Messages from '../pages/communication/Messages'
import Announcements from '../pages/communication/Announcements'
import Documents from '../pages/communication/Documents'
import Meetings from '../pages/communication/Meetings'

// Personal Features
import PersonalProfile from '../pages/personal/PersonalProfile'
import PersonalMessages from '../pages/personal/PersonalMessages'
import PersonalCalendar from '../pages/personal/PersonalCalendar'

// Enhanced KPI Dashboards - now using HierarchicalKPIDashboard
import { KPIDashboard } from './lazyRoutes'

// Test Pages - Removed non-existent imports

interface HRManagerRoutesProps {
  language: 'ar' | 'en'
}

export default function HRManagerRoutes({ language }: HRManagerRoutesProps) {
  return (
    <Routes>
      {/* Dashboard - Using Real KPI System */}
      <Route path="/" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <KPIDashboard dashboardType="hr" />
        </RoleBasedRoute>
      } />
      <Route path="/hr/dashboard" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <KPIDashboard dashboardType="hr" />
        </RoleBasedRoute>
      } />

      {/* Legacy Dashboard (for reference) */}
      <Route path="/hr/legacy-dashboard" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <HRManagerDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* Core Management (HR-specific) */}
      <Route path="/hr/employees" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <HREmployees language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/departments" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <HRDepartments language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/customers" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <HRCustomers language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/reports" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <HRReports language={language} />
        </RoleBasedRoute>
      } />

      {/* HR Management (Full Access) */}
      <Route path="/hr/leave" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <LeaveManagement language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/attendance" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <Attendance language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/performance" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <Performance language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/payroll" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <Payroll language={language} />
        </RoleBasedRoute>
      } />

      {/* Enhanced HR KPI Analytics - Each with specific KPI type */}
      <Route path="/hr/kpi/performance" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <KPIDashboard dashboardType="hr" kpiFilters={{ kpiType: "performance" }} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/kpi/retention" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <KPIDashboard dashboardType="hr" kpiFilters={{ kpiType: "retention" }} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/kpi/attendance" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <KPIDashboard dashboardType="hr" kpiFilters={{ kpiType: "attendance" }} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/kpi/satisfaction" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <KPIDashboard dashboardType="hr" kpiFilters={{ kpiType: "satisfaction" }} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/kpi/compliance" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <KPIDashboard dashboardType="hr" kpiFilters={{ kpiType: "compliance" }} />
        </RoleBasedRoute>
      } />

      {/* Personal Features */}
      <Route path="/hr/profile" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <PersonalProfile language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/messages" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <PersonalMessages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/calendar" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <PersonalCalendar language={language} />
        </RoleBasedRoute>
      } />

      {/* Communication */}
      <Route path="/hr/communication/messages" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <Messages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/communication/announcements" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <Announcements language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/communication/documents" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <Documents language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/hr/communication/meetings" element={
        <RoleBasedRoute requiredRole="hr_manager" fallbackPath="/hr/unauthorized">
          <Meetings language={language} />
        </RoleBasedRoute>
      } />

      {/* Test Pages - Removed non-existent components */}


      {/* Unauthorized Access */}
      <Route path="/hr/unauthorized" element={<Unauthorized language={language} />} />

      {/* Catch all - redirect to HR dashboard */}
      <Route path="*" element={<Navigate to="/hr/dashboard" replace />} />
    </Routes>
  )
}
