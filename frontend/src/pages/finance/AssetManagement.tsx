import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Package, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Filter,
  Download,
  Upload,
  Calendar,
  DollarSign,
  MapPin,
  User,
  Settings,
  TrendingDown
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface Asset {
  id: number;
  asset_id: string;
  name: string;
  name_ar: string;
  category: number;
  category_name: string;
  serial_number: string;
  model: string;
  manufacturer: string;
  purchase_date: string;
  purchase_price: string;
  current_book_value: number;
  accumulated_depreciation: number;
  depreciation_method: string;
  useful_life_years: number;
  status: string;
  condition: string;
  assigned_to: number | null;
  assigned_to_name: string;
  department: number | null;
  department_name: string;
  location: string;
  warranty_expiry: string | null;
  is_under_warranty: boolean;
  is_maintenance_due: boolean;
  currency_code: string;
  currency_symbol: string;
  tags: string;
  created_at: string;
}

interface AssetCategory {
  id: number;
  name: string;
  name_ar: string;
}

interface Department {
  id: number;
  name: string;
}

interface Employee {
  id: number;
  user: {
    first_name: string;
    last_name: string;
  };
}

const AssetManagement: React.FC = () => {
  const { language } = useLanguage();
  const [assets, setAssets] = useState<Asset[]>([]);
  const [categories, setCategories] = useState<AssetCategory[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);

  // Form state
  const [assetForm, setAssetForm] = useState({
    name: '',
    name_ar: '',
    category: '',
    serial_number: '',
    model: '',
    manufacturer: '',
    purchase_date: '',
    purchase_price: '',
    salvage_value: '',
    useful_life_years: 5,
    depreciation_method: 'STRAIGHT_LINE',
    assigned_to: '',
    department: '',
    location: '',
    warranty_expiry: '',
    condition: 'GOOD',
    status: 'AVAILABLE',
    tags: '',
    notes: ''
  });

  useEffect(() => {
    fetchAssets();
    fetchCategories();
    fetchDepartments();
    fetchEmployees();
  }, []);

  const fetchAssets = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter && statusFilter !== '__all__') params.append('status', statusFilter);
      if (categoryFilter && categoryFilter !== '__all__') params.append('category', categoryFilter);
      
      const response = await fetch(`/api/assets/?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setAssets(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching assets:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/asset-categories/');
      if (response.ok) {
        const data = await response.json();
        setCategories(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchDepartments = async () => {
    try {
      const response = await fetch('/api/departments/');
      if (response.ok) {
        const data = await response.json();
        setDepartments(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/employees/');
      if (response.ok) {
        const data = await response.json();
        setEmployees(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
    }
  };

  const handleAddAsset = async () => {
    try {
      const response = await fetch('/api/assets/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...assetForm,
          category: parseInt(assetForm.category),
          assigned_to: assetForm.assigned_to ? parseInt(assetForm.assigned_to) : null,
          department: assetForm.department ? parseInt(assetForm.department) : null,
          useful_life_years: parseInt(assetForm.useful_life_years.toString()),
        }),
      });

      if (response.ok) {
        fetchAssets();
        setIsAddDialogOpen(false);
        resetForm();
      }
    } catch (error) {
      console.error('Error adding asset:', error);
    }
  };

  const resetForm = () => {
    setAssetForm({
      name: '',
      name_ar: '',
      category: '',
      serial_number: '',
      model: '',
      manufacturer: '',
      purchase_date: '',
      purchase_price: '',
      salvage_value: '',
      useful_life_years: 5,
      depreciation_method: 'STRAIGHT_LINE',
      assigned_to: '',
      department: '',
      location: '',
      warranty_expiry: '',
      condition: 'GOOD',
      status: 'AVAILABLE',
      tags: '',
      notes: ''
    });
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'AVAILABLE': 'bg-green-100 text-green-800',
      'IN_USE': 'bg-blue-100 text-blue-800',
      'MAINTENANCE': 'bg-yellow-100 text-yellow-800',
      'RETIRED': 'bg-gray-100 text-gray-800',
      'LOST': 'bg-red-100 text-red-800',
      'DAMAGED': 'bg-red-100 text-red-800',
      'DISPOSED': 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getConditionColor = (condition: string) => {
    const colors = {
      'EXCELLENT': 'text-green-600',
      'GOOD': 'text-blue-600',
      'FAIR': 'text-yellow-600',
      'POOR': 'text-orange-600',
      'DAMAGED': 'text-red-600'
    };
    return colors[condition as keyof typeof colors] || 'text-gray-600';
  };

  const formatCurrency = (amount: number, symbol: string = '﷼') => {
    return `${amount.toLocaleString()} ${symbol}`;
  };

  const filteredAssets = assets.filter(asset => {
    const matchesSearch = !searchTerm || 
      asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.asset_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.serial_number.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || statusFilter === '__all__' || asset.status === statusFilter;
    const matchesCategory = !categoryFilter || categoryFilter === '__all__' || asset.category.toString() === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة الأصول' : 'Asset Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة وتتبع أصول الشركة والاستهلاك والصيانة'
              : 'Manage and track company assets, depreciation, and maintenance'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'إضافة أصل' : 'Add Asset'}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {language === 'ar' ? 'إضافة أصل جديد' : 'Add New Asset'}
                </DialogTitle>
              </DialogHeader>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>{language === 'ar' ? 'اسم الأصل' : 'Asset Name'}</Label>
                  <Input
                    value={assetForm.name}
                    onChange={(e) => setAssetForm({...assetForm, name: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل اسم الأصل' : 'Enter asset name'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الاسم بالعربية' : 'Arabic Name'}</Label>
                  <Input
                    value={assetForm.name_ar}
                    onChange={(e) => setAssetForm({...assetForm, name_ar: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل الاسم بالعربية' : 'Enter Arabic name'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الفئة' : 'Category'}</Label>
                  <Select value={assetForm.category} onValueChange={(value) => setAssetForm({...assetForm, category: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder={language === 'ar' ? 'اختر الفئة' : 'Select category'} />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {language === 'ar' ? category.name_ar : category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الرقم التسلسلي' : 'Serial Number'}</Label>
                  <Input
                    value={assetForm.serial_number}
                    onChange={(e) => setAssetForm({...assetForm, serial_number: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل الرقم التسلسلي' : 'Enter serial number'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الطراز' : 'Model'}</Label>
                  <Input
                    value={assetForm.model}
                    onChange={(e) => setAssetForm({...assetForm, model: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل الطراز' : 'Enter model'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الشركة المصنعة' : 'Manufacturer'}</Label>
                  <Input
                    value={assetForm.manufacturer}
                    onChange={(e) => setAssetForm({...assetForm, manufacturer: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل الشركة المصنعة' : 'Enter manufacturer'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'تاريخ الشراء' : 'Purchase Date'}</Label>
                  <Input
                    type="date"
                    value={assetForm.purchase_date}
                    onChange={(e) => setAssetForm({...assetForm, purchase_date: e.target.value})}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'سعر الشراء' : 'Purchase Price'}</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={assetForm.purchase_price}
                    onChange={(e) => setAssetForm({...assetForm, purchase_price: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل سعر الشراء' : 'Enter purchase price'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'العمر الافتراضي (سنوات)' : 'Useful Life (Years)'}</Label>
                  <Input
                    type="number"
                    min="1"
                    value={assetForm.useful_life_years}
                    onChange={(e) => setAssetForm({...assetForm, useful_life_years: parseInt(e.target.value) || 5})}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'طريقة الاستهلاك' : 'Depreciation Method'}</Label>
                  <Select value={assetForm.depreciation_method} onValueChange={(value) => setAssetForm({...assetForm, depreciation_method: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="STRAIGHT_LINE">
                        {language === 'ar' ? 'القسط الثابت' : 'Straight Line'}
                      </SelectItem>
                      <SelectItem value="DECLINING_BALANCE">
                        {language === 'ar' ? 'الرصيد المتناقص' : 'Declining Balance'}
                      </SelectItem>
                      <SelectItem value="NO_DEPRECIATION">
                        {language === 'ar' ? 'بدون استهلاك' : 'No Depreciation'}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'المخصص له' : 'Assigned To'}</Label>
                  <Select value={assetForm.assigned_to} onValueChange={(value) => setAssetForm({...assetForm, assigned_to: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder={language === 'ar' ? 'اختر الموظف' : 'Select employee'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">
                        {language === 'ar' ? 'غير مخصص' : 'Unassigned'}
                      </SelectItem>
                      {employees.map(employee => (
                        <SelectItem key={employee.id} value={employee.id.toString()}>
                          {employee.user.first_name} {employee.user.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'القسم' : 'Department'}</Label>
                  <Select value={assetForm.department} onValueChange={(value) => setAssetForm({...assetForm, department: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder={language === 'ar' ? 'اختر القسم' : 'Select department'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">
                        {language === 'ar' ? 'بدون قسم' : 'No department'}
                      </SelectItem>
                      {departments.map(department => (
                        <SelectItem key={department.id} value={department.id.toString()}>
                          {department.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="md:col-span-2">
                  <Label>{language === 'ar' ? 'الموقع' : 'Location'}</Label>
                  <Input
                    value={assetForm.location}
                    onChange={(e) => setAssetForm({...assetForm, location: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل الموقع' : 'Enter location'}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleAddAsset}>
                  {language === 'ar' ? 'إضافة الأصل' : 'Add Asset'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الأصول...' : 'Search assets...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="AVAILABLE">
                  {language === 'ar' ? 'متاح' : 'Available'}
                </SelectItem>
                <SelectItem value="IN_USE">
                  {language === 'ar' ? 'قيد الاستخدام' : 'In Use'}
                </SelectItem>
                <SelectItem value="MAINTENANCE">
                  {language === 'ar' ? 'تحت الصيانة' : 'Maintenance'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الفئات' : 'All categories'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الفئات' : 'All categories'}
                </SelectItem>
                {categories.map(category => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {language === 'ar' ? category.name_ar : category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Assets Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {language === 'ar' ? 'قائمة الأصول' : 'Assets List'}
            <Badge variant="secondary" className="ml-2">
              {filteredAssets.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'رقم الأصل' : 'Asset ID'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الاسم' : 'Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الفئة' : 'Category'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة الفنية' : 'Condition'}</TableHead>
                  <TableHead>{language === 'ar' ? 'القيمة الحالية' : 'Book Value'}</TableHead>
                  <TableHead>{language === 'ar' ? 'المخصص له' : 'Assigned To'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAssets.map((asset) => (
                  <TableRow key={asset.id}>
                    <TableCell className="font-medium">{asset.asset_id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{asset.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {asset.model} • {asset.manufacturer}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{asset.category_name}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(asset.status)}>
                        {language === 'ar' 
                          ? asset.status === 'AVAILABLE' ? 'متاح' 
                            : asset.status === 'IN_USE' ? 'قيد الاستخدام'
                            : asset.status === 'MAINTENANCE' ? 'تحت الصيانة'
                            : asset.status
                          : asset.status.replace('_', ' ')
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className={getConditionColor(asset.condition)}>
                        {language === 'ar' 
                          ? asset.condition === 'EXCELLENT' ? 'ممتاز' 
                            : asset.condition === 'GOOD' ? 'جيد'
                            : asset.condition === 'FAIR' ? 'مقبول'
                            : asset.condition
                          : asset.condition
                        }
                      </span>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {formatCurrency(asset.current_book_value, asset.currency_symbol)}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center">
                          <TrendingDown className="h-3 w-3 mr-1" />
                          {formatCurrency(asset.accumulated_depreciation, asset.currency_symbol)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {asset.assigned_to_name || (language === 'ar' ? 'غير مخصص' : 'Unassigned')}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {asset.location}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Calendar className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AssetManagement;
