import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Target,
  Calculator,
  Lightbulb,
  Activity,
  Zap,
  BarChart3
} from 'lucide-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { kpiAlgorithmService, KPIPrediction, TrendAnalysis, KPIRecommendation } from '@/services/kpiAlgorithmService'
// Language is passed as prop, not from context

interface KPIAlgorithmDashboardProps {
  kpiId: string
  kpiName: string
  currentValue?: number
  language: 'ar' | 'en'
}

export default function KPIAlgorithmDashboard({ kpiId, kpiName, currentValue, language }: KPIAlgorithmDashboardProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [predictions, setPredictions] = useState<KPIPrediction[]>([])
  const [trendAnalysis, setTrendAnalysis] = useState<TrendAnalysis | null>(null)
  const [recommendations, setRecommendations] = useState<KPIRecommendation[]>([])
  const [anomalyResult, setAnomalyResult] = useState<any>(null)

  const t = {
    algorithmicInsights: language === 'ar' ? 'الرؤى الخوارزمية' : 'Algorithmic Insights',
    predictions: language === 'ar' ? 'التنبؤات' : 'Predictions',
    trendAnalysis: language === 'ar' ? 'تحليل الاتجاه' : 'Trend Analysis',
    recommendations: language === 'ar' ? 'التوصيات' : 'Recommendations',
    anomalyDetection: language === 'ar' ? 'كشف الشذوذ' : 'Anomaly Detection',
    calculateAutomated: language === 'ar' ? 'حساب تلقائي' : 'Calculate Automated',
    refreshAnalysis: language === 'ar' ? 'تحديث التحليل' : 'Refresh Analysis',
    confidence: language === 'ar' ? 'الثقة' : 'Confidence',
    trend: language === 'ar' ? 'الاتجاه' : 'Trend',
    improving: language === 'ar' ? 'تحسن' : 'Improving',
    declining: language === 'ar' ? 'تراجع' : 'Declining',
    stable: language === 'ar' ? 'مستقر' : 'Stable',
    priority: language === 'ar' ? 'الأولوية' : 'Priority',
    impact: language === 'ar' ? 'التأثير' : 'Impact',
    high: language === 'ar' ? 'عالي' : 'High',
    medium: language === 'ar' ? 'متوسط' : 'Medium',
    low: language === 'ar' ? 'منخفض' : 'Low',
    noAnomalyDetected: language === 'ar' ? 'لم يتم اكتشاف شذوذ' : 'No Anomaly Detected',
    anomalyDetected: language === 'ar' ? 'تم اكتشاف شذوذ' : 'Anomaly Detected',
    checkAnomaly: language === 'ar' ? 'فحص الشذوذ' : 'Check Anomaly'
  }

  useEffect(() => {
    loadAlgorithmicData()
  }, [kpiId])

  const loadAlgorithmicData = async () => {
    setIsLoading(true)
    try {
      const [predictionsData, trendData, recommendationsData] = await Promise.all([
        kpiAlgorithmService.predictValues(kpiId, 6),
        kpiAlgorithmService.analyzeTrend(kpiId, 12),
        kpiAlgorithmService.getRecommendations(kpiId)
      ])

      setPredictions(predictionsData.predictions)
      setTrendAnalysis(trendData.trend_analysis)
      setRecommendations(recommendationsData.recommendations)
    } catch (error) {
      console.error('Failed to load algorithmic data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCalculateAutomated = async () => {
    try {
      setIsLoading(true)
      const result = await kpiAlgorithmService.calculateAutomated(kpiId)
      if (result.success) {
        // Refresh data after calculation
        await loadAlgorithmicData()
      }
    } catch (error) {
      console.error('Failed to calculate automated KPI:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCheckAnomaly = async () => {
    if (!currentValue) return
    
    try {
      const result = await kpiAlgorithmService.detectAnomaly(kpiId, currentValue)
      setAnomalyResult(result.anomaly_detection)
    } catch (error) {
      console.error('Failed to check anomaly:', error)
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-400" />
      case 'stable': return <Activity className="h-4 w-4 text-blue-400" />
      default: return <Target className="h-4 w-4 text-gray-400" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'medium': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'low': return 'bg-green-500/20 text-green-400 border-green-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'action': return <Zap className="h-4 w-4" />
      case 'analysis': return <Brain className="h-4 w-4" />
      case 'optimization': return <Target className="h-4 w-4" />
      case 'urgent': return <AlertTriangle className="h-4 w-4" />
      default: return <Lightbulb className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Brain className="h-5 w-5" />
              {t.algorithmicInsights}
            </CardTitle>
            <div className="flex gap-2">
              <Button 
                onClick={handleCalculateAutomated}
                disabled={isLoading}
                className="glass-button"
              >
                <Calculator className="h-4 w-4 mr-2" />
                {t.calculateAutomated}
              </Button>
              <Button 
                onClick={loadAlgorithmicData}
                disabled={isLoading}
                variant="outline"
                className="glass-button"
              >
                {t.refreshAnalysis}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <Tabs
        defaultValue="predictions"
        className="space-y-4"
        onValueChange={(value) => {
          console.log(`Tab changed to: ${value}`)
          // You can add analytics tracking or other side effects here
        }}
      >
        <TabsList className="glass-card border-white/20 w-full">
          <TabsTrigger value="predictions" className="flex-1">
            <BarChart3 className="h-4 w-4 mr-2" />
            {t.predictions}
          </TabsTrigger>
          <TabsTrigger value="trend" className="flex-1">
            <TrendingUp className="h-4 w-4 mr-2" />
            {t.trendAnalysis}
          </TabsTrigger>
          <TabsTrigger value="recommendations" className="flex-1">
            <Lightbulb className="h-4 w-4 mr-2" />
            {t.recommendations}
          </TabsTrigger>
          <TabsTrigger value="anomaly" className="flex-1">
            <AlertTriangle className="h-4 w-4 mr-2" />
            {t.anomalyDetection}
          </TabsTrigger>
        </TabsList>

        {/* Predictions Tab */}
        <TabsContent value="predictions">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white">{t.predictions}</CardTitle>
            </CardHeader>
            <CardContent>
              {predictions.length > 0 ? (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={predictions}>
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis 
                        dataKey="period" 
                        stroke="rgba(255,255,255,0.7)"
                        fontSize={12}
                      />
                      <YAxis 
                        stroke="rgba(255,255,255,0.7)"
                        fontSize={12}
                      />
                      <Tooltip 
                        contentStyle={{
                          backgroundColor: 'rgba(0,0,0,0.8)',
                          border: '1px solid rgba(255,255,255,0.2)',
                          borderRadius: '8px',
                          color: 'white'
                        }}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="predicted_value" 
                        stroke="#3b82f6" 
                        strokeWidth={2}
                        dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="text-center text-gray-400 py-8">
                  No prediction data available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trend Analysis Tab */}
        <TabsContent value="trend">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white">{t.trendAnalysis}</CardTitle>
            </CardHeader>
            <CardContent>
              {trendAnalysis ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      {getTrendIcon(trendAnalysis.trend)}
                      <span className="text-white font-medium">
                        {t.trend}: {t[trendAnalysis.trend as keyof typeof t] || trendAnalysis.trend}
                      </span>
                    </div>
                    <Badge className="glass-button">
                      {t.confidence}: {trendAnalysis.confidence}%
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="glass-card border-white/10 p-4">
                      <div className="text-sm text-gray-400">Slope</div>
                      <div className="text-lg font-semibold text-white">{trendAnalysis.slope.toFixed(4)}</div>
                    </div>
                    <div className="glass-card border-white/10 p-4">
                      <div className="text-sm text-gray-400">R-Squared</div>
                      <div className="text-lg font-semibold text-white">{trendAnalysis.r_squared.toFixed(3)}</div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-400 py-8">
                  No trend analysis available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Recommendations Tab */}
        <TabsContent value="recommendations">
          <div className="space-y-4">
            {recommendations.map((rec, index) => (
              <Card key={index} className="glass-card border-white/20">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-1">
                      {getTypeIcon(rec.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-white font-medium">{rec.title}</h3>
                        <Badge className={getPriorityColor(rec.priority)}>
                          {t[rec.priority as keyof typeof t] || rec.priority}
                        </Badge>
                        <Badge variant="outline" className="text-gray-400">
                          {t.impact}: {t[rec.estimated_impact as keyof typeof t] || rec.estimated_impact}
                        </Badge>
                      </div>
                      <p className="text-gray-300 text-sm">{rec.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            {recommendations.length === 0 && (
              <Card className="glass-card border-white/20">
                <CardContent className="p-8 text-center text-gray-400">
                  No recommendations available
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Anomaly Detection Tab */}
        <TabsContent value="anomaly">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white">{t.anomalyDetection}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {currentValue && (
                  <Button 
                    onClick={handleCheckAnomaly}
                    className="glass-button w-full"
                  >
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    {t.checkAnomaly} ({currentValue})
                  </Button>
                )}
                
                {anomalyResult && (
                  <div className={`p-4 rounded-lg border ${
                    anomalyResult.is_anomaly 
                      ? 'bg-red-500/10 border-red-500/30 text-red-400' 
                      : 'bg-green-500/10 border-green-500/30 text-green-400'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-4 w-4" />
                      <span className="font-medium">
                        {anomalyResult.is_anomaly ? t.anomalyDetected : t.noAnomalyDetected}
                      </span>
                    </div>
                    
                    {anomalyResult.is_anomaly && (
                      <div className="text-sm space-y-1">
                        <div>Type: {anomalyResult.anomaly_type}</div>
                        <div>Z-Score: {anomalyResult.z_score}</div>
                        <div>Confidence: {(anomalyResult.confidence * 100).toFixed(1)}%</div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
